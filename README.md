# Limo Booking Application Documentation

## Overview

The Limo Booking Application is a dual-purpose booking system that can run as both a standalone React application and a WordPress plugin. It provides a user-friendly interface for customers to book limousine services, select vehicles, and manage their bookings.

## Table of Contents

1. [Directory Structure](./DIRECTORY_STRUCTURE.md)
2. [API Documentation](./API_DOCUMENTATION.md)
3. [Development Guide](./DEVELOPMENT_GUIDE.md)
4. [Theme Management](./THEME_MANAGEMENT.md)
5. [Production Progress](./production_progress.md)
6. [Documentation Structure](./documentation_structure.md)

## Quick Links

- [Setup and Installation](#setup-and-installation)
- [Running the Development Server](./DEVELOPMENT_GUIDE.md#running-the-development-server)
- [Building for Production](./DEVELOPMENT_GUIDE.md#building-for-production)
- [API Endpoints](./API_DOCUMENTATION.md#api-endpoints)
- [Troubleshooting](./DEVELOPMENT_GUIDE.md#troubleshooting)

## Project Structure

```
project-root/
├── src/                       # Source code
│   ├── core/                  # Shared core functionality
│   │   ├── components/        # Shared React components
│   │   ├── hooks/             # Shared custom hooks
│   │   ├── utils/             # Shared utility functions
│   │   ├── types/             # Shared TypeScript types
│   │   ├── store/             # Shared state management (Zustand)
│   │   ├── services/          # API integrations and services
│   │   ├── styles/            # Shared styles
│   │   ├── config/            # Configuration files
│   │   └── lib/               # Third-party library integrations
│   │
│   ├── standalone/            # Standalone React app specific code
│   │   ├── App.tsx            # Standalone app root component
│   │   ├── main.tsx           # Standalone entry point
│   │   ├── routes/            # React Router routes
│   │   ├── pages/             # Standalone app pages
│   │   └── styles/            # Standalone-specific styles
│   │
│   ├── wordpress/             # WordPress specific code
│   │   ├── index.tsx          # WordPress entry point
│   │   └── styles/            # WordPress-specific styles
│   │
│   ├── components/            # Application-wide components
│   ├── styles/                # Global styles
│   ├── types/                 # Global TypeScript types
│   ├── assets/                # Static assets
│   ├── config/                # Build configurations
│   ├── main.tsx               # Main entry point for standalone
│   └── wordpress.tsx          # Main entry point for WordPress
│
├── wordpress/
│   └── limo-booking/          # Canonical WordPress plugin directory
│
├── config/                    # Build configurations
├── dist/                      # Build output
├── public/                    # Public assets
├── vehicles/                  # Canonical vehicle data and assets
├── docs/                      # Documentation
└── test/                      # Test files
```

## Core Components

### BookingForm

The main booking form component that handles the entire booking process. It consists of several sub-components:

- **index.tsx**: Main container component that orchestrates the booking flow
- **BookingForm.tsx**: Core form component
- **ProgressSteps.tsx**: Shows the booking progress steps
- **StepIndicator.tsx**: Individual step indicator
- **MobileStepper.tsx**: Mobile-friendly stepper component
- **BookingTabs.tsx**: Tab navigation for the booking form
- **MapComponent.tsx**: Interactive map for location selection using Mapbox
- **VehicleSelection.tsx**: Vehicle selection interface
- **ThemeSelection.tsx**: Theme selection for the booking form
- **TripSummary.tsx**: Summary of the booking details
- **PaymentStep.tsx**: Payment processing step
- **SpecialRequests.tsx**: Special requests input
- **BookingConfirmation.tsx**: Booking confirmation page
- **ThankYouPage.tsx**: Thank you page after successful booking

### UI Components

The application uses a collection of reusable UI components located in `src/core/components/ui/`.

## State Management

The application uses Zustand for state management with two main stores:

- **bookingStore.ts**: Manages the booking data and process
- **themeStore.ts**: Manages the theme and styling options

## API Integrations

The application integrates with several external APIs:

- **hereApi.ts**: Integration with HERE Maps API for location services
- **mapboxApi.ts**: Integration with Mapbox for maps and geocoding
- **api.ts**: Core API service for booking and data management
- **themeService.ts**: Service for theme management
- **diagnostics.ts**: Diagnostic services for debugging

## Deployment Modes

### Standalone Application

The standalone version runs as a complete React application. Key files:

- **src/standalone/App.tsx**: Main application component
- **src/standalone/main.tsx**: Entry point
- **src/main.tsx**: Main entry point that imports the standalone app

### WordPress Plugin

The WordPress version runs as a plugin within WordPress. Key files:

- **src/wordpress/index.tsx**: WordPress-specific implementation
- **src/wordpress.tsx**: Entry point for WordPress integration

## Build System

The project uses Vite as the build tool with multiple configuration options:

- **vite.config.js**: Main Vite configuration
- **config/vite.standalone.ts**: Configuration for standalone build
- **config/vite.wordpress.ts**: Configuration for WordPress plugin build

## Environment Variables

The application uses different environment files for different deployment modes:

- **.env.shared**: Shared variables between both versions
- **.env.standalone**: Standalone app specific variables
- **.env.wordpress**: WordPress plugin specific variables

## Setup and Installation

### Prerequisites

- Node.js (v16+)
- npm or yarn
- WordPress installation (for WordPress plugin mode)

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Set up environment variables:
   - Copy `.env.example` to `.env`
   - Update the variables with your values

### Development

#### Standalone App
```bash
npm run dev:standalone
```

#### WordPress Plugin
```bash
npm run dev:wordpress
```

### Building

#### Standalone App
```bash
npm run build:standalone
```
Output will be in `dist/standalone/`

#### WordPress Plugin
```bash
npm run build:wordpress
```
Output will be in `wordpress/limo-booking/wp-booking/`

#### Build Both
```bash
npm run build
```

### WordPress Plugin Installation

1. Build the WordPress plugin
2. Copy the `wordpress/limo-booking` directory to your WordPress plugins directory
3. Activate the plugin in WordPress admin

## Available Scripts

- `npm run dev:standalone`: Start standalone development server
- `npm run dev:wordpress`: Start WordPress development server
- `npm run build:standalone`: Build standalone app
- `npm run build:wordpress`: Build WordPress plugin
- `npm run build`: Build both versions
- `npm run preview:standalone`: Preview standalone build
- `npm run preview:wordpress`: Preview WordPress build
- `npm run lint`: Lint the codebase

## Technologies Used

- **React**: UI library
- **TypeScript**: Type-safe JavaScript
- **Zustand**: State management
- **React Hook Form**: Form handling
- **Zod**: Schema validation
- **Tailwind CSS**: Utility-first CSS framework
- **Mapbox GL**: Interactive maps
- **HERE Maps API**: Location services
- **Vite**: Build tool
- **ESLint**: Code linting
- **React Router**: Routing for standalone app
- **Headless UI**: Unstyled, accessible UI components
- **Framer Motion**: Animation library
- **React Datepicker**: Date picking component
- **React Beautiful DnD**: Drag and drop functionality

## Customization

### Themes

The application supports custom themes through the `themeStore.ts` and `ThemeSelection.tsx` components. Themes can be customized in the admin interface or through code.

### Vehicle Types

Vehicle types and their details can be configured in the vehicles directory.

## Troubleshooting

For common issues and their solutions, refer to the diagnostic tools in `src/core/services/diagnostics.ts`.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited. 