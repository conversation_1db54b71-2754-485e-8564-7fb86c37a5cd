# Limo Booking Form - WordPress Integration

## Overview

This WordPress integration is designed to maintain the exact same design, layout, and behavior as the standalone React version of the Limo Booking Form. Instead of creating a custom HTML implementation, this approach directly integrates the React components into WordPress with minimal changes.

## Integration Approach

The integration follows these key principles:

1. **Direct React Component Integration**: The original React components are used directly without modification, preserving all functionality and behavior.

2. **Consistent Styling**: CSS is carefully managed to ensure the form looks identical in both standalone and WordPress environments.

3. **Theme Compatibility**: The form respects WordPress theme settings while maintaining its visual integrity.

4. **Minimal Overhead**: The integration is lightweight and doesn't add unnecessary code or dependencies.

## Usage

To add the booking form to any WordPress page or post, use the shortcode:

```
[limo_booking_form]
```

You can customize the form with these attributes:

```
[limo_booking_form type="point-to-point" theme="dark" class="custom-class"]
```

### Shortcode Attributes

- `type`: The booking form type (point-to-point, hourly, airport, multi-day)
- `theme`: The color theme (light, dark)
- `class`: Additional CSS classes to add to the container

## Technical Implementation

The integration works by:

1. Loading the React bundle and CSS assets on pages with the shortcode
2. Creating a container element with the appropriate attributes
3. Initializing the React application within this container
4. Passing WordPress configuration to the React components

The `class-react-integration.php` file handles the WordPress side of the integration, while `init-wp.ts` manages the React initialization.

## Styling

The integration uses a combination of approaches to maintain consistent styling:

1. Core React component styles are preserved
2. WordPress-specific CSS ensures theme compatibility
3. CSS variables allow for theme customization while maintaining the form's design integrity

## Customization

The form's appearance can be customized through the WordPress admin settings page. These settings are passed to the React components, allowing for customization while maintaining the form's core design principles.