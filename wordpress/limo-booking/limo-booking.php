<?php

/**
 * Plugin Name: Limo Booking
 * Plugin URI: https://wwmobilitysolutions.com/
 * Description: A modern booking form for limousine services with React integration.
 * Version: 2.0.0
 * Author: WWMS
 * Author URI: https://wwmobilitysolutions.com/
 * Text Domain: limo-booking
 * Requires at least: 5.8
 * Requires PHP: 7.4
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Define plugin constants
if (!defined('LIMO_BOOKING_VERSION')) {
    define('LIMO_BOOKING_VERSION', '2.0.1');
}

define('LIMO_BOOKING_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('LIMO_BOOKING_PLUGIN_URL', plugin_dir_url(__FILE__));
define('LIMO_BOOKING_BUILD_DATE', '2025-03-08');
define('LIMO_BOOKING_MIN_WP_VERSION', '5.8');
define('LIMO_BOOKING_MIN_PHP_VERSION', '7.4');
define('LIMO_BOOKING_ASSETS_REGISTERED', false);

// Add diagnostic logging
function limo_booking_diagnostic_log($message, $data = []) {
    if (defined('WP_DEBUG') && WP_DEBUG === true) {
        error_log('[Limo Booking] ' . $message . (empty($data) ? '' : ' - ' . json_encode($data)));
    }
}

// Register plugin assets
function limo_booking_register_assets() {
    if (defined('LIMO_BOOKING_ASSETS_REGISTERED') && LIMO_BOOKING_ASSETS_REGISTERED) {
        return;
    }

    // NUCLEAR ISOLATION: Use nuclear.js instead of wordpress.js for complete isolation
    $js_path = LIMO_BOOKING_PLUGIN_DIR . 'public/nuclear.js';
    $css_path = LIMO_BOOKING_PLUGIN_DIR . 'public/wordpress.css'; // CSS fallback

    // Check if nuclear JS exists, fallback to wordpress files
    if (!file_exists($js_path)) {
        $js_path = LIMO_BOOKING_PLUGIN_DIR . 'public/wordpress.js';
        limo_booking_diagnostic_log('Nuclear JS not found, falling back to wordpress.js', ['path' => $js_path]);
    }

    // Final check if files exist
    if (!file_exists($css_path)) {
        limo_booking_diagnostic_log('Error: CSS file not found', ['path' => $css_path]);
        return;
    }

    if (!file_exists($js_path)) {
        limo_booking_diagnostic_log('Error: JavaScript file not found', ['path' => $js_path]);
        return;
    }

    // Determine which files we're using
    $using_nuclear = strpos($js_path, 'nuclear.js') !== false;

    // Nuclear version has CSS bundled in JS, so we only need minimal CSS
    $css_url = LIMO_BOOKING_PLUGIN_URL . 'public/wordpress.css'; // Always use this for basic styles
    $js_url = $using_nuclear ?
        LIMO_BOOKING_PLUGIN_URL . 'public/nuclear.js' :
        LIMO_BOOKING_PLUGIN_URL . 'public/wordpress.js';

    wp_register_style(
        'limo-booking-public',
        $css_url,
        array(),
        LIMO_BOOKING_VERSION
    );

    wp_register_script(
        'limo-booking-public',
        $js_url,
        $using_nuclear ? array() : array('wp-element'), // Nuclear version doesn't need wp-element
        LIMO_BOOKING_VERSION,
        true
    );

    limo_booking_diagnostic_log('Assets registered', [
        'using_nuclear' => $using_nuclear,
        'css_url' => $css_url,
        'js_url' => $js_url
    ]);

    define('LIMO_BOOKING_ASSETS_REGISTERED', true);
}
add_action('init', 'limo_booking_register_assets');

// Enqueue assets when shortcode is used
function limo_booking_maybe_enqueue_assets() {
    if (did_action('limo_booking_shortcode_used')) {
        if (!wp_style_is('limo-booking-public', 'registered')) {
            limo_booking_diagnostic_log('Error: Style not registered', ['handle' => 'limo-booking-public']);
            // Try to register it now as fallback
            limo_booking_register_assets();
        }

        if (!wp_script_is('limo-booking-public', 'registered')) {
            limo_booking_diagnostic_log('Error: Script not registered', ['handle' => 'limo-booking-public']);
            // Try to register it now as fallback
            limo_booking_register_assets();
        }

        wp_enqueue_style('limo-booking-public');
        wp_enqueue_script('limo-booking-public');

        // Add inline fallback styles in case CSS fails to load
        // Get theme settings for dynamic styling
        $settings = get_option('limo_booking_settings', array());

        // Use smart theme generation with fallbacks
        $primary_color = $settings['primary_color'] ?? '#765a3d';
        $background_color = $settings['background'] ?? '#000000';
        $text_color = $settings['text_primary'] ?? '#ffffff';
        $border_color = $settings['surface_dark'] ?? '#333333';

        // If we have a theme preset, use the preset colors
        if (isset($settings['theme_preset']) && $settings['theme_preset'] !== 'custom') {
            $presets = [
                'classic_dark' => ['primary' => '#765a3d', 'background' => '#000000'],
                'modern_light' => ['primary' => '#1565c0', 'background' => '#ffffff'],
                'elegant_black' => ['primary' => '#333333', 'background' => '#121212'],
                'luxury_gold' => ['primary' => '#d4af37', 'background' => '#1a1a1a'],
                'corporate_blue' => ['primary' => '#0066cc', 'background' => '#f8f9fa']
            ];

            $preset = $presets[$settings['theme_preset']] ?? $presets['classic_dark'];
            $primary_color = $preset['primary'];
            $background_color = $preset['background'];

            // Auto-generate text colors based on background brightness
            $is_light_bg = limo_booking_is_light_color($background_color);
            $text_color = $is_light_bg ? '#1f2937' : '#ffffff';
            $border_color = $is_light_bg ? '#e5e7eb' : '#333333';
        }

        // Add bulletproof inline styles with high specificity
        wp_add_inline_style('limo-booking-public', '
            /* BULLETPROOF WORDPRESS PLUGIN CONTAINER STYLES */
            .limo-booking-plugin.limo-booking-plugin-container {
                /* CSS Variables for dynamic theming */
                --limo-primary: ' . esc_attr($primary_color) . ';
                --limo-background: ' . esc_attr($background_color) . ';
                --limo-text-primary: ' . esc_attr($text_color) . ';
                --limo-border: ' . esc_attr($border_color) . ';

                /* Container styling with high specificity */
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
                padding: 1rem !important;
                margin: 1rem 0 !important;
                border: 1px solid var(--limo-border) !important;
                border-radius: 12px !important;
                min-height: 400px !important;
                background: var(--limo-background) !important;
                color: var(--limo-text-primary) !important;
                box-sizing: border-box !important;
                position: relative !important;
                overflow: hidden !important;
                width: 100% !important;
                max-width: 1200px !important;
                margin-left: auto !important;
                margin-right: auto !important;
            }

            /* Loading state */
            .limo-booking-plugin.limo-booking-plugin-container[data-ready="false"]:after {
                content: "Loading booking form..." !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                background: var(--limo-background) !important;
                color: var(--limo-text-primary) !important;
                font-size: 16px !important;
                z-index: 10 !important;
            }

            /* Force all child elements to use border-box */
            .limo-booking-plugin.limo-booking-plugin-container *,
            .limo-booking-plugin.limo-booking-plugin-container *::before,
            .limo-booking-plugin.limo-booking-plugin-container *::after {
                box-sizing: border-box !important;
            }
        ');
    }
}
add_action('wp_footer', 'limo_booking_maybe_enqueue_assets', 5);

/**
 * Helper function to determine if a color is light or dark
 */
function limo_booking_is_light_color($hex) {
    $hex = ltrim($hex, '#');
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));

    // Calculate brightness using standard formula
    $brightness = ($r * 299 + $g * 587 + $b * 114) / 1000;

    return $brightness > 128;
}

// Log plugin loading
limo_booking_diagnostic_log('Plugin loading', [
    'version' => LIMO_BOOKING_VERSION,
    'build_date' => LIMO_BOOKING_BUILD_DATE
]);

/**
 * Check if all required files exist before including them
 * This prevents fatal errors if files are missing
 */
function limo_booking_check_required_files() {
    $required_files = [
        'includes/class-limo-booking-theme.php' => 'Theme class',
        'admin/class-theme-manager.php' => 'Theme manager',
        'includes/class-salesmate-integration.php' => 'Salesmate integration',
        'admin/templates/settings-page.php' => 'Settings page template',
        'includes/class-react-integration.php' => 'React Integration Class',
        'admin/class-limo-booking-admin.php' => 'Admin Settings Class'
    ];

    $missing_files = [];
    foreach ($required_files as $file => $description) {
        $file_path = LIMO_BOOKING_PLUGIN_DIR . $file;
        if (!file_exists($file_path)) {
            $missing_files[] = $file;
            limo_booking_diagnostic_log('Missing required file', [
                'file' => $file_path,
                'description' => $description
            ]);
        }
    }

    if (!empty($missing_files)) {
        limo_booking_diagnostic_log('Plugin activation aborted due to missing files', [
            'missing_files' => $missing_files
        ]);
        return false;
    }

    return true;
}

/**
 * The core plugin class.
 */
class Limo_Booking {
    /**
     * The unique instance of the plugin.
     */
    private static $instance = null;

    /**
     * Instance of the Limo_Booking_Admin class.
     * @var Limo_Booking_Admin
     */
    private $admin_instance;

    /**
     * Gets the instance of this class.
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize the plugin.
     */
    private function __construct() {
        // This will be initialized only if all required files are present
        if (limo_booking_check_required_files()) {
            try {
                // Include required files in a safe way
                // NOTE: We don't include class-limo-booking.php because the main class is already defined in this file

                // Now require the theme class
                require_once LIMO_BOOKING_PLUGIN_DIR . 'includes/class-limo-booking-theme.php';

                // Initialize other required files
                require_once LIMO_BOOKING_PLUGIN_DIR . 'admin/class-theme-manager.php';
                require_once LIMO_BOOKING_PLUGIN_DIR . 'includes/class-salesmate-integration.php';

                // Ensure React integration class is loaded
                if (file_exists(LIMO_BOOKING_PLUGIN_DIR . 'includes/class-react-integration.php')) {
                    require_once LIMO_BOOKING_PLUGIN_DIR . 'includes/class-react-integration.php';
                    // Initialize the React Integration class
                    $GLOBALS['limo_booking_react_integration'] = new Limo_Booking_React_Integration();
                } else {
                    limo_booking_diagnostic_log('Critical: Missing includes/class-react-integration.php');
                }

                // Load and instantiate Admin class
                if (file_exists(LIMO_BOOKING_PLUGIN_DIR . 'admin/class-limo-booking-admin.php')) {
                    require_once LIMO_BOOKING_PLUGIN_DIR . 'admin/class-limo-booking-admin.php';
                    $this->admin_instance = new Limo_Booking_Admin();
                } else {
                    limo_booking_diagnostic_log('Critical: Missing admin/class-limo-booking-admin.php');
                    // Optionally, handle the absence of the admin class, e.g., by not adding admin hooks.
                }

                // Register hooks
                $this->init_hooks();
            } catch (Exception $e) {
                limo_booking_diagnostic_log('Error initializing plugin', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
    }

    /**
     * Register all necessary hooks.
     */
    private function init_hooks() {
        // Activation and deactivation hooks
        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);

        // Add theme manager class
        $this->load_theme_manager();

        // Actions
        add_action('init', [$this, 'init']);
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_scripts']);

        // Shortcodes - register all variants for backward compatibility
        add_shortcode('limo_booking', [$this, 'render_booking_form']);
        add_shortcode('limo_booking_form', [$this, 'render_booking_form']);
        add_shortcode('limo_form', [$this, 'render_booking_form']);
    }

    /**
     * Safely load the theme manager class
     */
    private function load_theme_manager() {
        // Try to load the original theme manager class
        $theme_manager_path = LIMO_BOOKING_PLUGIN_DIR . 'admin/class-theme-manager.php';

        if (file_exists($theme_manager_path)) {
            try {
                require_once $theme_manager_path;
            } catch (Exception $e) {
                limo_booking_diagnostic_log('Error loading theme manager', [
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Try to load the simplified theme manager as fallback
        $simplified_theme_manager_path = LIMO_BOOKING_PLUGIN_DIR . 'admin/class-limo-booking-theme-manager.php';

        if (file_exists($simplified_theme_manager_path)) {
            try {
                require_once $simplified_theme_manager_path;
            } catch (Exception $e) {
                limo_booking_diagnostic_log('Error loading simplified theme manager', [
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Initialize the plugin when WordPress loads.
     */
    public function init() {
        // Register settings
        $this->register_settings();

        // Register REST API routes
        add_action('rest_api_init', [$this, 'register_rest_routes']);
    }

    /**
     * Activate the plugin.
     */
    public function activate() {
        limo_booking_diagnostic_log('Plugin activated', [
            'version' => LIMO_BOOKING_VERSION,
            'build_date' => LIMO_BOOKING_BUILD_DATE,
            'php_version' => PHP_VERSION,
            'wp_version' => get_bloginfo('version')
        ]);

        // Flush rewrite rules to ensure our REST API endpoints work
        flush_rewrite_rules();
    }

    /**
     * Deactivate the plugin.
     */
    public function deactivate() {
        limo_booking_diagnostic_log('Plugin deactivated');
        flush_rewrite_rules();
    }

    /**
     * Register REST API routes.
     */
    public function register_rest_routes() {
        // Security Note: For public endpoints like this, ensure the handler ('handle_booking')
        // performs rigorous input sanitization and validation on all received data.
        // Nonce verification is not typically used for public anonymous submissions,
        // but rate limiting and spam protection should be considered at a higher level (e.g., server/WAF).
        register_rest_route('limo-booking/v1', '/booking', [
            'methods' => 'POST',
            'callback' => [$this, 'handle_booking'],
            'permission_callback' => '__return_true',
        ]);
    }

    /**
     * Add admin menu.
     */
    public function add_admin_menu() {
        if ($this->admin_instance && method_exists($this->admin_instance, 'render_settings_page')) {
            add_menu_page(
                'Limo Booking',
                'Limo Booking',
                'manage_options',
                'limo-booking',
                [$this->admin_instance, 'render_settings_page'],
                'dashicons-calendar-alt',
                30
            );
        } else {
            limo_booking_diagnostic_log('Admin menu not added because admin instance or render_settings_page method is missing.');
        }
    }

    /**
     * Register plugin settings.
     * This is now largely handled by Limo_Booking_Admin.
     * Kept for clarity or potential future use for non-admin settings.
     */
    public function register_settings() {
        // The main settings are registered in Limo_Booking_Admin via an admin_init hook.
        // However, if there were a need to change the option name for a specific reason:
        // register_setting('limo_booking_settings', 'limo_booking_settings');
        // For now, this method will be kept empty or for other non-admin settings.
    }

    // The render_admin_page method has been removed, as its functionality is now in Limo_Booking_Admin.

    /**
     * Handle booking form submission.
     */
    public function handle_booking($request) {
        $params = $request->get_params();
        $sanitized_data = [];

        // Sanitize common booking form fields
        if (isset($params['name'])) {
            $sanitized_data['name'] = sanitize_text_field($params['name']);
        }
        if (isset($params['email'])) {
            $sanitized_data['email'] = sanitize_email($params['email']);
        }
        if (isset($params['phone'])) {
            $sanitized_data['phone'] = sanitize_text_field($params['phone']); // Basic sanitization for phone
        }
        if (isset($params['pickup_address'])) {
            $sanitized_data['pickup_address'] = sanitize_text_field($params['pickup_address']);
        }
        if (isset($params['dropoff_address'])) {
            $sanitized_data['dropoff_address'] = sanitize_text_field($params['dropoff_address']);
        }
        if (isset($params['booking_date'])) {
            // Sanitize as text, actual date validation should happen during processing/validation logic
            $sanitized_data['booking_date'] = sanitize_text_field($params['booking_date']);
        }
        if (isset($params['booking_time'])) { // Assuming time might be a separate field
            $sanitized_data['booking_time'] = sanitize_text_field($params['booking_time']);
        }
        if (isset($params['service_type'])) {
            $sanitized_data['service_type'] = sanitize_key($params['service_type']); // 'point-to-point', 'hourly' etc.
        }
        if (isset($params['passengers'])) {
            $sanitized_data['passengers'] = absint($params['passengers']); // Ensure it's a positive integer
        }
        if (isset($params['special_requests'])) {
            $sanitized_data['special_requests'] = sanitize_textarea_field($params['special_requests']);
        }
        // Add any other expected fields from your React form here.
        // Example: $sanitized_data['vehicle_type'] = sanitize_key($params['vehicle_type'] ?? '');

        // Replace original data with sanitized data for logging and further processing
        $booking_data = $sanitized_data;

        limo_booking_diagnostic_log('Booking request received (sanitized)', [
            'data' => $booking_data // Log sanitized data
        ]);

        // Further processing should use $booking_data (the sanitized version)
        // TODO: Implement actual booking logic (e.g., save to database, send email) using $booking_data.

        return rest_ensure_response([
            'success' => true,
            'message' => 'Booking received and data sanitized.' // Updated message
        ]);
    }

    /**
     * Enqueue scripts and styles.
     */
    public function enqueue_scripts() {
        // Styles are now handled by the React Integration class when needed
        // This method is kept for backwards compatibility, but no longer enqueues
        // scripts directly to avoid conflicts

        // Instead of directly enqueuing, we'll trigger the React Integration to handle it
        if (isset($GLOBALS['limo_booking_react_integration'])) {
            $GLOBALS['limo_booking_react_integration']->enqueue_react_assets();
        } else {
            limo_booking_diagnostic_log('React Integration class not initialized - scripts may not be properly loaded');
        }
    }

    /**
     * Render booking form.
     */
    public function render_booking_form($atts) {
        // Signal that the shortcode is being used
        do_action('limo_booking_shortcode_used');

        // Log deprecation notice only for the old shortcode name
        if (did_action('wp') === 1 && current_filter() === 'limo_booking') {
            error_log('LIMO_BOOKING_DEPRECATED_SHORTCODE: The [limo_booking] shortcode is deprecated. Please use [limo_booking_form] instead.');
        }

        // Ensure React integration class is available
        if (!class_exists('Limo_Booking_React_Integration')) {
            $integration_file = LIMO_BOOKING_PLUGIN_DIR . 'includes/class-react-integration.php';
            if (file_exists($integration_file)) {
                require_once $integration_file;
                // Initialize the class if it's not initialized yet
                if (!isset($GLOBALS['limo_booking_react_integration'])) {
                    $GLOBALS['limo_booking_react_integration'] = new Limo_Booking_React_Integration();
                }
            } else {
                return '<p>Error: Limo Booking React Integration not found.</p>';
            }
        }

        // Use the react integration's render method directly if it exists
        if (isset($GLOBALS['limo_booking_react_integration']) &&
            method_exists($GLOBALS['limo_booking_react_integration'], 'render_booking_form')) {
            return $GLOBALS['limo_booking_react_integration']->render_booking_form($atts);
        }

        // Fallback if we can't use the react integration class
        // Get attributes, provide defaults
        $atts = shortcode_atts([
            'form_type' => 'point-to-point', // Default from old shortcode
            'type' => 'point-to-point',      // Default from new shortcode ([limo_booking_form])
            'theme' => 'dark',               // Default from new shortcode ([limo_booking_form])
            'class' => '',                   // Default from new shortcode ([limo_booking_form])
        ], $atts, 'limo_booking');

        // Generate a unique ID for this form instance
        $form_id = 'limo-booking-form-' . uniqid();

        // Create the container with the proper structure
        $output = '<div id="' . esc_attr($form_id) . '" ';
        $output .= 'class="limo-booking-plugin ' . esc_attr($atts['class']) . '" ';
        $output .= 'data-theme="' . esc_attr($atts['theme']) . '" ';
        $output .= 'data-form-type="' . esc_attr($atts['type']) . '" ';
        $output .= 'data-ready="false">';
        $output .= '</div>';

        // Add initialization script with better error handling
        $output .= '<script>'
            . '(function() {'
            . '  var maxAttempts = 20;'
            . '  var attempts = 0;'
            . '  var interval = setInterval(function() {'
            . '    if (typeof limoBookingInit === "function") {'
            . '      clearInterval(interval);'
            . '      try {'
            . '        limoBookingInit("' . esc_js($form_id) . '", "' . esc_js($atts['type']) . '");'
            . '        document.getElementById("' . esc_js($form_id) . '").dataset.ready = "true";'
            . '      } catch (e) {'
            . '        console.error("Limo Booking: Error initializing form:", e);'
            . '      }'
            . '    } else if (++attempts >= maxAttempts) {'
            . '      clearInterval(interval);'
            . '      console.error("Limo Booking: Failed to initialize after " + attempts + " attempts");'
            . '      var container = document.getElementById("' . esc_js($form_id) . '");'
            . '      if (container) {'
            . '        container.innerHTML = "<p style=\"text-align:center;padding:2rem;color:#666;\">Unable to load booking form. Please refresh the page.</p>";'
            . '      }'
            . '    }'
            . '  }, 250);'
            . '})();'
            . '</script>';

        return $output;
    }
}

// Initialize the plugin
function limo_booking_init() {
    try {
        $limo_booking = Limo_Booking::get_instance();
    } catch (Exception $e) {
        limo_booking_diagnostic_log('Error initializing plugin', [
            'error' => $e->getMessage()
        ]);
    }
}

// Hook into WordPress init to initialize our plugin
add_action('plugins_loaded', 'limo_booking_init');