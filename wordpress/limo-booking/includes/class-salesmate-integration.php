<?php
/**
 * Salesmate CRM Integration for Limo Booking
 *
 * @package LimoBooking
 */

if (!defined('WPINC')) {
    die;
}

class Salesmate_Integration {
    /**
     * Salesmate API credentials
     */
    private $api_url;
    private $api_key;
    private $workspace_token;

    /**
     * Constructor
     */
    public function __construct() {
        $settings = get_option('limo_booking_settings', array());
        $this->api_url = isset($settings['salesmate_api_url']) ? $settings['salesmate_api_url'] : '';
        $this->api_key = isset($settings['salesmate_api_key']) ? $settings['salesmate_api_key'] : '';
        $this->workspace_token = isset($settings['salesmate_workspace_token']) ? $settings['salesmate_workspace_token'] : '';
    }

    /**
     * Format booking data for Salesmate
     */
    public function format_booking_data($booking_data) {
        return array(
            'deal' => array(
                'title' => sprintf('Limo Booking - %s', $booking_data['contact_info']['name']),
                'description' => $this->format_booking_description($booking_data),
                'pipeline_id' => $this->get_pipeline_id(),
                'stage_id' => $this->get_initial_stage_id(),
                'owner_id' => $this->get_default_owner_id(),
                'value' => 0, // Will be updated when pricing is implemented
                'currency' => 'USD',
                'source' => 'Website Booking Form'
            ),
            'contact' => array(
                'first_name' => $this->get_first_name($booking_data['contact_info']['name']),
                'last_name' => $this->get_last_name($booking_data['contact_info']['name']),
                'emails' => array(
                    array(
                        'type' => 'work',
                        'value' => $booking_data['contact_info']['email']
                    )
                ),
                'phones' => array(
                    array(
                        'type' => 'work',
                        'value' => $booking_data['contact_info']['phone']
                    )
                )
            ),
            'custom_fields' => array(
                'service_type' => $booking_data['service_type'],
                'pickup_location' => $booking_data['pickup']['address'],
                'pickup_datetime' => $booking_data['pickup']['time'],
                'dropoff_location' => isset($booking_data['dropoff']['address']) ? $booking_data['dropoff']['address'] : '',
                'vehicle_type' => $booking_data['vehicle_type'],
                'passengers' => $booking_data['passengers'],
                'special_requests' => isset($booking_data['special_requests']) ? $booking_data['special_requests'] : ''
            )
        );
    }

    /**
     * Send booking data to Salesmate
     */
    public function send_booking_to_salesmate($booking_data) {
        if (empty($this->api_key) || empty($this->workspace_token)) {
            return new WP_Error('salesmate_not_configured', 'Salesmate API is not properly configured');
        }

        $formatted_data = $this->format_booking_data($booking_data);
        
        $response = wp_remote_post($this->get_api_endpoint('deals'), array(
            'headers' => $this->get_request_headers(),
            'body' => json_encode($formatted_data),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);
        
        if (wp_remote_retrieve_response_code($response) !== 201) {
            return new WP_Error(
                'salesmate_api_error',
                isset($body['message']) ? $body['message'] : 'Unknown error occurred',
                $body
            );
        }

        return $body;
    }

    /**
     * Format booking description for Salesmate
     */
    private function format_booking_description($booking_data) {
        $description = sprintf(
            "Service Type: %s\n" .
            "Pickup Location: %s\n" .
            "Pickup Time: %s\n",
            $booking_data['service_type'],
            $booking_data['pickup']['address'],
            $booking_data['pickup']['time']
        );

        if (isset($booking_data['dropoff']['address'])) {
            $description .= sprintf("Dropoff Location: %s\n", $booking_data['dropoff']['address']);
        }

        $description .= sprintf(
            "Vehicle Type: %s\n" .
            "Passengers: %d\n",
            $booking_data['vehicle_type'],
            $booking_data['passengers']
        );

        if (!empty($booking_data['special_requests'])) {
            $description .= sprintf("Special Requests: %s\n", $booking_data['special_requests']);
        }

        return $description;
    }

    /**
     * Get Salesmate API endpoint URL
     */
    private function get_api_endpoint($resource) {
        return trailingslashit($this->api_url) . 'v3/' . $resource;
    }

    /**
     * Get request headers for Salesmate API
     */
    private function get_request_headers() {
        return array(
            'Content-Type' => 'application/json',
            'x-api-key' => $this->api_key,
            'workspaceToken' => $this->workspace_token
        );
    }

    /**
     * Get first name from full name
     */
    private function get_first_name($full_name) {
        $name_parts = explode(' ', trim($full_name), 2);
        return $name_parts[0];
    }

    /**
     * Get last name from full name
     */
    private function get_last_name($full_name) {
        $name_parts = explode(' ', trim($full_name), 2);
        return isset($name_parts[1]) ? $name_parts[1] : '';
    }

    /**
     * Get pipeline ID from settings or use default
     */
    private function get_pipeline_id() {
        $settings = get_option('limo_booking_settings', array());
        return isset($settings['salesmate_pipeline_id']) ? $settings['salesmate_pipeline_id'] : '1';
    }

    /**
     * Get initial stage ID from settings or use default
     */
    private function get_initial_stage_id() {
        $settings = get_option('limo_booking_settings', array());
        return isset($settings['salesmate_initial_stage_id']) ? $settings['salesmate_initial_stage_id'] : '1';
    }

    /**
     * Get default owner ID from settings
     */
    private function get_default_owner_id() {
        $settings = get_option('limo_booking_settings', array());
        return isset($settings['salesmate_default_owner_id']) ? $settings['salesmate_default_owner_id'] : '1';
    }

    /**
     * Test Salesmate API connection
     */
    public function test_connection() {
        if (empty($this->api_key) || empty($this->workspace_token)) {
            return new WP_Error('salesmate_not_configured', 'Salesmate API is not properly configured');
        }

        $response = wp_remote_get($this->get_api_endpoint('me'), array(
            'headers' => $this->get_request_headers(),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        if (wp_remote_retrieve_response_code($response) !== 200) {
            $body = json_decode(wp_remote_retrieve_body($response), true);
            return new WP_Error(
                'salesmate_api_error',
                isset($body['message']) ? $body['message'] : 'Unable to connect to Salesmate API',
                $body
            );
        }

        return true;
    }
}