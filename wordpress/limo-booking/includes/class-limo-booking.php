<?php
/**
 * Core plugin class for Limo Booking
 *
 * @package LimoBooking
 */

if (!defined('WPINC')) {
    die;
}

class Limo_Booking {
    /**
     * The unique instance of the plugin.
     */
    private static $instance = null;

    /**
     * Instance of the Limo_Booking_Admin class.
     */
    private $admin_instance;

    /**
     * Instance of the Limo_Booking_Theme class.
     */
    private $theme_instance;

    /**
     * Instance of the React_Integration class.
     */
    private $react_integration;

    /**
     * Gets the instance of this class.
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor.
     */
    private function __construct() {
        $this->load_dependencies();
        $this->setup_hooks();
        $this->init_components();
    }

    /**
     * Load required dependencies.
     */
    private function load_dependencies() {
        require_once LIMO_BOOKING_PLUGIN_DIR . 'admin/class-limo-booking-admin.php';
        require_once LIMO_BOOKING_PLUGIN_DIR . 'includes/class-limo-booking-theme.php';
        require_once LIMO_BOOKING_PLUGIN_DIR . 'includes/class-react-integration.php';
        require_once LIMO_BOOKING_PLUGIN_DIR . 'includes/class-salesmate-integration.php';
    }

    /**
     * Setup WordPress hooks.
     */
    private function setup_hooks() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_shortcode('limo_booking_form', array($this, 'render_booking_form'));
        add_action('admin_notices', array($this, 'check_dependencies'));
    }

    /**
     * Initialize plugin components.
     */
    private function init_components() {
        $this->admin_instance = new Limo_Booking_Admin();
        $this->theme_instance = new Limo_Booking_Theme();
        $this->react_integration = new Limo_Booking_React_Integration();
    }

    /**
     * Initialize plugin.
     */
    public function init() {
        load_plugin_textdomain('limo-booking', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }

    /**
     * Add admin menu items.
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Limo Booking', 'limo-booking'),
            __('Limo Booking', 'limo-booking'),
            'manage_options',
            'limo-booking',
            array($this->admin_instance, 'render_settings_page'),
            'dashicons-calendar-alt',
            30
        );
    }

    /**
     * Render the booking form shortcode.
     */
    public function render_booking_form($atts) {
        if (!is_admin()) {
            return $this->react_integration->render_booking_form($atts);
        }
        return '';
    }

    // Asset enqueuing is now handled by the main plugin file and React integration class

    /**
     * Get frontend settings.
     */
    private function get_frontend_settings() {
        $settings = get_option('limo_booking_settings', array());
        return array(
            'theme' => $this->theme_instance->get_current_theme(),
            'api_keys' => array(
                'mapbox' => isset($settings['mapbox_api_key']) ? $settings['mapbox_api_key'] : '',
                'here_maps' => isset($settings['here_maps_api_key']) ? $settings['here_maps_api_key'] : '',
            ),
            'webhook_url' => isset($settings['webhook_url']) ? $settings['webhook_url'] : '',
            'vehicle_types' => $this->get_vehicle_types(),
        );
    }

    /**
     * Get available vehicle types.
     */
    private function get_vehicle_types() {
        $settings = get_option('limo_booking_settings', array());
        $vehicle_types = isset($settings['vehicle_types']) ? $settings['vehicle_types'] : array();

        if (empty($vehicle_types)) {
            $vehicle_types = array(
                'sedan' => array(
                    'name' => __('Sedan', 'limo-booking'),
                    'capacity' => 4,
                    'image' => '',
                ),
                'suv' => array(
                    'name' => __('SUV', 'limo-booking'),
                    'capacity' => 6,
                    'image' => '',
                ),
                'van' => array(
                    'name' => __('Van', 'limo-booking'),
                    'capacity' => 12,
                    'image' => '',
                ),
                'stretch' => array(
                    'name' => __('Stretch Limousine', 'limo-booking'),
                    'capacity' => 8,
                    'image' => '',
                ),
            );
        }

        return $vehicle_types;
    }

    /**
     * Check plugin dependencies and display notices if needed.
     */
    public function check_dependencies() {
        if (version_compare(PHP_VERSION, LIMO_BOOKING_MIN_PHP_VERSION, '<')) {
            $message = sprintf(
                __('Limo Booking requires PHP version %s or higher. You are running version %s', 'limo-booking'),
                LIMO_BOOKING_MIN_PHP_VERSION,
                PHP_VERSION
            );
            echo '<div class="error"><p>' . esc_html($message) . '</p></div>';
        }

        if (version_compare($GLOBALS['wp_version'], LIMO_BOOKING_MIN_WP_VERSION, '<')) {
            $message = sprintf(
                __('Limo Booking requires WordPress version %s or higher. You are running version %s', 'limo-booking'),
                LIMO_BOOKING_MIN_WP_VERSION,
                $GLOBALS['wp_version']
            );
            echo '<div class="error"><p>' . esc_html($message) . '</p></div>';
        }
    }

    /**
     * Activation hook callback.
     */
    public static function activate() {
        // Add default settings if they don't exist
        if (!get_option('limo_booking_settings')) {
            update_option('limo_booking_settings', array(
                'mapbox_api_key' => '',
                'here_maps_api_key' => '',
                'webhook_url' => '',
                'vehicle_types' => array(),
                'theme' => array(
                    'primary_color' => '#007bff',
                    'secondary_color' => '#6c757d',
                    'background_color' => '#ffffff',
                    'text_color' => '#212529',
                ),
            ));
        }

        // Clear permalinks
        flush_rewrite_rules();
    }

    /**
     * Deactivation hook callback.
     */
    public static function deactivate() {
        flush_rewrite_rules();
    }
}