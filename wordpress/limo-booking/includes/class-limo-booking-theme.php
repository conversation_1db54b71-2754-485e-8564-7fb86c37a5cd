<?php
/**
 * Theme handling class for Limo Booking
 *
 * @package LimoBooking
 */

if (!defined('WPINC')) {
    die;
}

class Limo_Booking_Theme {
    /**
     * Default theme settings
     */
    private $default_theme = array(
        'primary_color' => '#007bff',
        'secondary_color' => '#6c757d',
        'background_color' => '#ffffff',
        'text_color' => '#212529',
        'surface_color' => '#f8f9fa',
        'mapbox_style' => 'mapbox://styles/mapbox/streets-v11'
    );

    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'register_theme_settings'));
        add_action('wp_head', array($this, 'output_custom_css'));
        add_action('admin_init', array($this, 'maybe_update_theme_settings'));
    }

    /**
     * Register theme settings
     */
    public function register_theme_settings() {
        register_setting(
            'limo_booking_settings',
            'limo_booking_theme_settings',
            array(
                'type' => 'array',
                'description' => 'Theme settings for Limo Booking',
                'sanitize_callback' => array($this, 'sanitize_theme_settings'),
                'default' => $this->default_theme
            )
        );
    }

    /**
     * Sanitize theme settings
     */
    public function sanitize_theme_settings($input) {
        $sanitized = array();

        // Sanitize colors (must be valid hex colors)
        $color_fields = array('primary_color', 'secondary_color', 'background_color', 'text_color', 'surface_color');
        foreach ($color_fields as $field) {
            if (isset($input[$field])) {
                $sanitized[$field] = sanitize_hex_color($input[$field]) ?: $this->default_theme[$field];
            } else {
                $sanitized[$field] = $this->default_theme[$field];
            }
        }

        // Sanitize Mapbox style URL
        if (isset($input['mapbox_style'])) {
            $sanitized['mapbox_style'] = esc_url_raw($input['mapbox_style']);
        } else {
            $sanitized['mapbox_style'] = $this->default_theme['mapbox_style'];
        }

        return $sanitized;
    }

    /**
     * Output custom CSS based on theme settings
     */
    public function output_custom_css() {
        if (!is_admin()) {
            $theme = $this->get_current_theme();
            ?>
            <style type="text/css">
                .limo-booking-form {
                    --primary-color: <?php echo esc_html($theme['primary_color']); ?>;
                    --secondary-color: <?php echo esc_html($theme['secondary_color']); ?>;
                    --background-color: <?php echo esc_html($theme['background_color']); ?>;
                    --text-color: <?php echo esc_html($theme['text_color']); ?>;
                    --surface-color: <?php echo esc_html($theme['surface_color']); ?>;
                }

                .limo-booking-form button.primary {
                    background-color: var(--primary-color);
                    color: white;
                }

                .limo-booking-form button.secondary {
                    background-color: var(--secondary-color);
                    color: white;
                }

                .limo-booking-form {
                    background-color: var(--background-color);
                    color: var(--text-color);
                }

                .limo-booking-form .surface {
                    background-color: var(--surface-color);
                }
            </style>
            <?php
        }
    }

    /**
     * Get current theme settings
     */
    public function get_current_theme() {
        $saved_theme = get_option('limo_booking_theme_settings', array());
        return wp_parse_args($saved_theme, $this->default_theme);
    }

    /**
     * Update theme settings if needed (for example, when adding new theme options)
     */
    public function maybe_update_theme_settings() {
        $current_theme = get_option('limo_booking_theme_settings', array());
        $needs_update = false;

        foreach ($this->default_theme as $key => $value) {
            if (!isset($current_theme[$key])) {
                $current_theme[$key] = $value;
                $needs_update = true;
            }
        }

        if ($needs_update) {
            update_option('limo_booking_theme_settings', $current_theme);
        }
    }

    /**
     * Get theme settings for frontend
     */
    public function get_theme_for_frontend() {
        $theme = $this->get_current_theme();
        return array(
            'colors' => array(
                'primary' => $theme['primary_color'],
                'secondary' => $theme['secondary_color'],
                'background' => $theme['background_color'],
                'text' => $theme['text_color'],
                'surface' => $theme['surface_color'],
            ),
            'mapboxStyle' => $theme['mapbox_style'],
        );
    }

    /**
     * Reset theme to defaults
     */
    public function reset_theme() {
        update_option('limo_booking_theme_settings', $this->default_theme);
    }
}