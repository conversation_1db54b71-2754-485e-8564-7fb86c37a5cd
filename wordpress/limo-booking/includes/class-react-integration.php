<?php

/**
 * React Integration Class
 *
 * This class handles the integration of the React booking form with WordPress
 * while maintaining the exact same design, layout, and behavior as the standalone version.
 *
 * @package Limo_Booking
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
  die;
}

class Limo_Booking_React_Integration
{

  /**
   * Initialize the class and set its properties.
   */
  public function __construct()
  {
    // Asset handling is now done in the main plugin file
    add_action('wp_enqueue_scripts', [$this, 'enqueue_react_assets']);
  }

  /**
   * Enqueue React assets when needed
   */
  public function enqueue_react_assets() {
    // Only enqueue if shortcode is being used
    if (!did_action('limo_booking_shortcode_used')) {
      return;
    }

    // Enqueue the main assets
    wp_enqueue_style('limo-booking-public');
    wp_enqueue_script('limo-booking-public');

    // Configure React assets with settings
    $this->configure_react_assets();
  }

  /**
   * Configure React assets with settings
   */
  private function configure_react_assets()
  {
      // Get plugin settings
      $settings = get_option('limo_booking_settings', array());

      // Enqueue Google Fonts (Raleway) to match the standalone version
      wp_enqueue_style(
        'limo-booking-google-fonts',
        'https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;500;600;700&display=swap',
        array(),
        null
      );

      // Pass configuration to the React app
      $config = array(
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'restUrl' => rest_url('limo-booking/v1'),
        'nonce' => wp_create_nonce('wp_rest'),
        'isWordPress' => true,
        'hideThemeSelector' => isset($settings['hide_theme_selector']) ? (bool)$settings['hide_theme_selector'] : false,
        // limo_booking_inherit_theme_colors is saved as 'yes'/'no'. Convert to boolean for React.
        'inheritThemeColors' => isset($settings['limo_booking_inherit_theme_colors']) ? ($settings['limo_booking_inherit_theme_colors'] === 'yes') : false,
        'theme' => array(
          // Default values match those in class-limo-booking-admin.php
          'primary_color' => $settings['theme_primary_color'] ?? '#765a3d',
          'primary_light' => $settings['theme_primary_light'] ?? '#8b6d4c',
          'primary_dark'  => $settings['theme_primary_dark'] ?? '#5d472f',
          'background'    => $settings['theme_background'] ?? '#000000',
          'surface'       => $settings['theme_surface'] ?? '#141414',
          'surface_dark'  => $settings['theme_surface_dark'] ?? '#1A1A1A',
          'text_primary'  => $settings['theme_text_primary'] ?? '#ffffff',
          'text_secondary'=> $settings['theme_text_secondary'] ?? '#e5e7eb',
          'text_muted'    => $settings['theme_text_muted'] ?? '#9ca3af',
          'text_disabled' => $settings['theme_text_disabled'] ?? 'rgba(255, 255, 255, 0.5)',
          'mapStyle'      => $settings['theme_map_style'] ?? 'mapbox://styles/mapbox/dark-v10',
          'mapTheme'      => $settings['theme_map_theme'] ?? 'dark',
        ),
        // Retrieve vehicle images, defaulting to an empty array.
        // The sanitization in class-limo-booking-admin.php should ensure all defined vehicle types
        // have keys, possibly with empty string values if no image is set.
        'vehicleImages' => isset($settings['vehicle_images']) && is_array($settings['vehicle_images'])
                            ? $settings['vehicle_images']
                            : [],
      );

      // Add API keys if available. Note: Admin saves as 'mapbox_api_key', not 'mapbox_token'.
      if (!empty($settings['mapbox_api_key'])) {
        $config['mapboxToken'] = $settings['mapbox_api_key']; // Use 'mapbox_api_key' from settings
      }
      if (!empty($settings['here_api_key'])) {
        $config['hereApiKey'] = $settings['here_api_key']; // This one is correct
      }

      // Allow other plugins to modify the config
      $config = apply_filters('limo_booking_react_config', $config, $settings);

      wp_localize_script('limo-booking-public', 'limoBookingConfig', $config);

      // Also add nuclear-specific configuration
      wp_add_inline_script('limo-booking-public',
        'window.limoBookingNuclearConfig = ' . wp_json_encode($config) . ';',
        'before'
      );
  }

  /**
   * Render the booking form shortcode.
   *
   * @param array $atts Shortcode attributes.
   * @return string The booking form HTML.
   */
  public function render_booking_form($atts = array())
  {
    // Signal that the shortcode is being used and configure React
    do_action('limo_booking_shortcode_used');
    $this->configure_react_assets();

    $atts = shortcode_atts(
      array(
        'type' => 'point-to-point', // Default booking type
        'theme' => 'dark', // Default theme
        'class' => '', // Additional CSS classes
        'id' => '', // Optional custom ID
      ),
      $atts,
      'limo_booking_form'
    );

    // Validate booking type
    $valid_types = array('point-to-point', 'hourly', 'airport', 'multi-day');
    $booking_type = in_array($atts['type'], $valid_types) ? $atts['type'] : 'point-to-point';

    // Validate theme
    $theme = in_array($atts['theme'], array('light', 'dark')) ? $atts['theme'] : 'dark';

    // Add the booking type to the global JS variable
    wp_add_inline_script(
      'limo-booking-public',
      'window.limoBookingFormType = "' . esc_js($booking_type) . '";',
      'before'
    );

    // Use custom ID if provided, otherwise generate one
    $form_id = !empty($atts['id']) ?
               sanitize_html_class($atts['id']) :
               'limo-booking-form-' . uniqid();

    // Create container with data attributes for React
    $output = sprintf(
      '<div id="%s" class="limo-booking-plugin %s" data-theme="%s" data-form-type="%s" data-ready="false"></div>',
      esc_attr($form_id),
      esc_attr($atts['class']),
      esc_attr($theme),
      esc_attr($booking_type)
    );

    // Add NUCLEAR initialization script with retry logic
    $output .= sprintf(
      '<script>
        (function() {
          var maxAttempts = 10;
          var attempts = 0;
          var interval = setInterval(function() {
            // Try multiple initialization approaches in order of preference
            if (typeof limoBookingNuclearInit === "function") {
              clearInterval(interval);
              console.log("INIT: Using nuclear isolation for complete WordPress isolation");
              limoBookingNuclearInit("%s", "%s");
              document.getElementById("%s").dataset.ready = "true";
            } else if (typeof limoBookingMinimalInit === "function") {
              clearInterval(interval);
              console.log("INIT: Using minimal standalone approach");
              limoBookingMinimalInit("%s", "%s");
              document.getElementById("%s").dataset.ready = "true";
            } else if (typeof limoBookingInit === "function") {
              clearInterval(interval);
              console.log("INIT: Falling back to regular initialization");
              limoBookingInit("%s", "%s");
              document.getElementById("%s").dataset.ready = "true";
            } else if (++attempts >= maxAttempts) {
              clearInterval(interval);
              console.error("Limo Booking: Failed to initialize after " + attempts + " attempts");
              var container = document.getElementById("%s");
              if (container) {
                container.innerHTML = "<div style=\"padding:20px;text-align:center;color:#666;\">Unable to load booking form. Please refresh the page.</div>";
              }
            }
          }, 500);
        })();
      </script>',
      esc_js($form_id),
      esc_js($booking_type),
      esc_js($form_id),
      esc_js($form_id),
      esc_js($booking_type),
      esc_js($form_id),
      esc_js($form_id),
      esc_js($booking_type),
      esc_js($form_id),
      esc_js($form_id)
    );

    return $output;
  }
}

// Don't auto-initialize the class - it will be initialized by the main plugin file
