<?php
/**
 * Simplified Theme Manager Class
 * A replacement for the original theme manager that won't cause errors
 */
class Limo_Booking_Theme_Manager {
    // Basic properties
    private $color_schemes;
    private $background_modes;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->initialize_theme_options();
    }
    
    /**
     * Initialize the basic theme options
     */
    private function initialize_theme_options() {
        $this->color_schemes = array(
            'classic_brown' => array(
                'name' => 'Classic Brown',
                'colors' => array(
                    'primary' => '#765a3d',
                )
            ),
            'ocean_blue' => array(
                'name' => 'Ocean Blue',
                'colors' => array(
                    'primary' => '#3b82f6',
                )
            ),
        );
        
        $this->background_modes = array(
            'dark' => array(
                'name' => 'Dark Mode',
                'colors' => array(
                    'background' => '#000000',
                )
            ),
            'light' => array(
                'name' => 'Light Mode',
                'colors' => array(
                    'background' => '#ffffff',
                )
            ),
        );
    }
    
    /**
     * Get theme configuration
     */
    public function get_theme_config() {
        return array(
            'primary_color' => '#765a3d',
            'background' => '#000000',
            'surface' => '#141414',
            'text_primary' => '#ffffff',
            'mapStyle' => 'mapbox://styles/mapbox/dark-v11',
            'isDark' => true
        );
    }
    
    /**
     * Render theme scheme field
     */
    public function render_theme_scheme_field() {
        $settings = get_option('limo_booking_options', array());
        $selected_scheme = isset($settings['theme_scheme']) ? $settings['theme_scheme'] : 'classic_brown';
        ?>
        <select name="limo_booking_options[theme_scheme]">
            <?php foreach ($this->color_schemes as $id => $scheme) : ?>
                <option value="<?php echo esc_attr($id); ?>" <?php selected($selected_scheme, $id); ?>>
                    <?php echo esc_html($scheme['name']); ?>
                </option>
            <?php endforeach; ?>
        </select>
        <?php
    }
    
    /**
     * Render background mode field
     */
    public function render_background_mode_field() {
        $settings = get_option('limo_booking_options', array());
        $selected_mode = isset($settings['background_mode']) ? $settings['background_mode'] : 'dark';
        ?>
        <select name="limo_booking_options[background_mode]">
            <?php foreach ($this->background_modes as $id => $mode) : ?>
                <option value="<?php echo esc_attr($id); ?>" <?php selected($selected_mode, $id); ?>>
                    <?php echo esc_html($mode['name']); ?>
                </option>
            <?php endforeach; ?>
        </select>
        <?php
    }
    
    /**
     * Render font colors field - simplified version
     */
    public function render_font_colors_field() {
        $settings = get_option('limo_booking_options', array());
        $selected_option = isset($settings['font_colors']) ? $settings['font_colors'] : 'light_on_dark';
        ?>
        <select name="limo_booking_options[font_colors]">
            <option value="light_on_dark" <?php selected($selected_option, 'light_on_dark'); ?>>
                Light on Dark
            </option>
            <option value="dark_on_light" <?php selected($selected_option, 'dark_on_light'); ?>>
                Dark on Light
            </option>
        </select>
        <?php
    }
    
    /**
     * Render map theme field - simplified version
     */
    public function render_map_theme_field() {
        $settings = get_option('limo_booking_options', array());
        $selected_theme = isset($settings['map_theme']) ? $settings['map_theme'] : 'dark';
        ?>
        <select name="limo_booking_options[map_theme]">
            <option value="dark" <?php selected($selected_theme, 'dark'); ?>>
                Dark Map
            </option>
            <option value="light" <?php selected($selected_theme, 'light'); ?>>
                Light Map
            </option>
        </select>
        <?php
    }
} 