jQuery(document).ready(function($) {
    // Add recursion guard to prevent infinite loops
    let isProcessingThemeUpdate = false;
    let lastThemeUpdateTime = 0;

    // Theme scheme selection
    $('.theme-scheme-card').on('click', function() {
        // Remove selected class from all cards
        $('.theme-scheme-card').removeClass('selected');
        // Add selected class to clicked card
        $(this).addClass('selected');
        // Check the radio button
        $(this).find('input[type="radio"]').prop('checked', true);
        
        // If this is the custom theme card, show the custom color inputs
        if ($(this).hasClass('custom-theme-card')) {
            // Make sure the custom theme is selected
            $('input[name="limo_booking_settings[theme_scheme]"][value="custom"]').prop('checked', true);
        }
    });

    // Custom color picker handling
    $('.custom-color-picker').on('input', function() {
        // Update the text input
        $(this).next('.custom-color-text').val($(this).val());
        // Update the preview
        updateCustomColorPreview();
        // Make sure the custom theme is selected
        $('.custom-theme-card').addClass('selected');
        $('input[name="limo_booking_settings[theme_scheme]"][value="custom"]').prop('checked', true);
    });

    $('.custom-color-text').on('input', function() {
        // Update the color picker
        $(this).prev('.custom-color-picker').val($(this).val());
        // Update the preview
        updateCustomColorPreview();
        // Make sure the custom theme is selected
        $('.custom-theme-card').addClass('selected');
        $('input[name="limo_booking_settings[theme_scheme]"][value="custom"]').prop('checked', true);
    });

    function updateCustomColorPreview() {
        const primaryColor = $('input[name="limo_booking_settings[custom_colors][primary]"]').val();
        const backgroundColor = $('input[name="limo_booking_settings[custom_colors][background]"]').val();
        
        $('.custom-theme-card .color-preview span').eq(0).css('background-color', primaryColor);
        $('.custom-theme-card .color-preview span').eq(1).css('background-color', backgroundColor);
    }

    // Initialize the preview on page load
    updateCustomColorPreview();

    // Background mode selection
    $('.background-mode-option').on('click', function() {
        // Check the radio button
        $(this).find('input[type="radio"]').prop('checked', true);
    });

    // Font option selection
    $('.font-option').on('click', function() {
        // Check the radio button
        $(this).find('input[type="radio"]').prop('checked', true);
    });

    // Map theme selection
    $('.map-theme-option').on('click', function() {
        // Check the radio button
        $(this).find('input[type="radio"]').prop('checked', true);
    });

    // Listen for theme updates from the React app using the new event name
    window.addEventListener('wp_admin_theme_update', function(event) {
        if (isProcessingThemeUpdate) {
            console.log('WordPress Admin: Already processing a theme update, ignoring this event');
            return;
        }
        
        try {
            isProcessingThemeUpdate = true;
            
            const theme = event.detail.theme;
            const source = event.detail.source || 'unknown';
            
            console.log('WordPress Admin: Theme update received from ' + source, theme);
    
            // Update color inputs if using custom theme
            if (theme.is_custom_theme) {
                $('input[name="limo_booking_settings[custom_colors][primary]"]').val(theme.primary_color);
                $('input[name="limo_booking_settings[custom_colors][background]"]').val(theme.background);
                
                // Select the custom theme radio button
                $('input[name="limo_booking_settings[theme_scheme]"][value="custom"]').prop('checked', true);
            }
    
            // Update the visual preview without submitting the form
            updateCustomColorPreview();
            console.log('WordPress Admin: Preview updated');
            
            // Add a submit button highlight to hint the user to save changes
            const submitButton = $('input[type="submit"]');
            submitButton.css('box-shadow', '0 0 8px #2271b1');
            setTimeout(function() {
                submitButton.css('box-shadow', 'none');
            }, 1500);
        } finally {
            // Always clear the recursion guard
            isProcessingThemeUpdate = false;
        }
    });
    
    // When form is submitted, dispatch an event to notify React app
    $('form#limo-booking-settings').on('submit', function() {
        console.log('WordPress Admin: Settings form submitted, notifying React app');
        
        // Get the current theme values from the form
        const formTheme = {
            primary_color: $('input[name="limo_booking_settings[custom_colors][primary]"]').val(),
            primary_light: adjustColor($('input[name="limo_booking_settings[custom_colors][primary]"]').val(), 20),
            primary_dark: adjustColor($('input[name="limo_booking_settings[custom_colors][primary]"]').val(), -20),
            background: $('input[name="limo_booking_settings[custom_colors][background]"]').val(),
            surface: adjustColor($('input[name="limo_booking_settings[custom_colors][background]"]').val(), 10),
            surface_dark: adjustColor($('input[name="limo_booking_settings[custom_colors][background]"]').val(), 15),
            // Default text colors based on background brightness
            text_primary: isLightColor($('input[name="limo_booking_settings[custom_colors][background]"]').val()) ? '#111827' : '#ffffff',
            text_secondary: isLightColor($('input[name="limo_booking_settings[custom_colors][background]"]').val()) ? '#374151' : '#e5e7eb',
            text_muted: isLightColor($('input[name="limo_booking_settings[custom_colors][background]"]').val()) ? '#6b7280' : '#9ca3af',
            text_disabled: isLightColor($('input[name="limo_booking_settings[custom_colors][background]"]').val()) ? '#9ca3af' : '#6b7280',
            mapStyle: 'mapbox://styles/mapbox/' + (isLightColor($('input[name="limo_booking_settings[custom_colors][background]"]').val()) ? 'light-v11' : 'dark-v11'),
            mapTheme: isLightColor($('input[name="limo_booking_settings[custom_colors][background]"]').val()) ? 'light' : 'dark',
            is_custom_theme: true,
            theme_scheme: 'custom'
        };
        
        // Helper functions for color manipulation
        function adjustColor(hex, percent) {
            try {
                // Convert hex to RGB
                let r = parseInt(hex.substring(1,3), 16);
                let g = parseInt(hex.substring(3,5), 16);
                let b = parseInt(hex.substring(5,7), 16);
                
                // Adjust each component
                r = Math.max(0, Math.min(255, r + percent));
                g = Math.max(0, Math.min(255, g + percent));
                b = Math.max(0, Math.min(255, b + percent));
                
                // Convert back to hex
                return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
            } catch (e) {
                console.error('Error adjusting color:', e);
                return hex;
            }
        }
        
        function isLightColor(hex) {
            try {
                const r = parseInt(hex.substring(1,3), 16);
                const g = parseInt(hex.substring(3,5), 16);
                const b = parseInt(hex.substring(5,7), 16);
                const brightness = (r * 299 + g * 587 + b * 114) / 1000;
                return brightness > 128;
            } catch (e) {
                console.error('Error determining color brightness:', e);
                return false;
            }
        }
    });
}); 