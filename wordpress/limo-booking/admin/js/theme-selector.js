/**
 * Interactive Theme Selector for Limo Booking Admin
 */

(function($) {
    'use strict';

    // Smart color calculation functions
    function isLightColor(hex) {
        const color = hex.replace('#', '');
        const r = parseInt(color.substr(0, 2), 16);
        const g = parseInt(color.substr(2, 2), 16);
        const b = parseInt(color.substr(4, 2), 16);
        
        const brightness = (r * 299 + g * 587 + b * 114) / 1000;
        return brightness > 128;
    }

    function lightenColor(hex, percent) {
        const color = hex.replace('#', '');
        const r = parseInt(color.substr(0, 2), 16);
        const g = parseInt(color.substr(2, 2), 16);
        const b = parseInt(color.substr(4, 2), 16);
        
        const newR = Math.min(255, r + (percent / 100) * (255 - r));
        const newG = Math.min(255, g + (percent / 100) * (255 - g));
        const newB = Math.min(255, b + (percent / 100) * (255 - b));
        
        return `#${Math.round(newR).toString(16).padStart(2, '0')}${Math.round(newG).toString(16).padStart(2, '0')}${Math.round(newB).toString(16).padStart(2, '0')}`;
    }

    function darkenColor(hex, percent) {
        const color = hex.replace('#', '');
        const r = parseInt(color.substr(0, 2), 16);
        const g = parseInt(color.substr(2, 2), 16);
        const b = parseInt(color.substr(4, 2), 16);
        
        const newR = Math.max(0, r - (percent / 100) * r);
        const newG = Math.max(0, g - (percent / 100) * g);
        const newB = Math.max(0, b - (percent / 100) * b);
        
        return `#${Math.round(newR).toString(16).padStart(2, '0')}${Math.round(newG).toString(16).padStart(2, '0')}${Math.round(newB).toString(16).padStart(2, '0')}`;
    }

    function generateSmartTheme(primaryColor, backgroundColor) {
        const isDarkBg = !isLightColor(backgroundColor);
        
        // Generate primary variations
        const primaryLight = lightenColor(primaryColor, 15);
        const primaryDark = darkenColor(primaryColor, 15);
        
        let surface, surfaceDark, textPrimary, textSecondary, textMuted, textDisabled, mapStyle, mapTheme;
        
        if (isDarkBg) {
            // Dark theme
            surface = lightenColor(backgroundColor, 8);
            surfaceDark = lightenColor(backgroundColor, 4);
            textPrimary = '#ffffff';
            textSecondary = '#e5e7eb';
            textMuted = '#9ca3af';
            textDisabled = 'rgba(255, 255, 255, 0.5)';
            mapStyle = 'mapbox://styles/mapbox/dark-v11';
            mapTheme = 'dark';
        } else {
            // Light theme
            surface = darkenColor(backgroundColor, 3);
            surfaceDark = darkenColor(backgroundColor, 8);
            textPrimary = '#1f2937';
            textSecondary = '#374151';
            textMuted = '#6b7280';
            textDisabled = 'rgba(31, 41, 55, 0.5)';
            mapStyle = 'mapbox://styles/mapbox/light-v11';
            mapTheme = 'light';
        }
        
        return {
            primary_color: primaryColor,
            primary_light: primaryLight,
            primary_dark: primaryDark,
            background: backgroundColor,
            surface: surface,
            surface_dark: surfaceDark,
            text_primary: textPrimary,
            text_secondary: textSecondary,
            text_muted: textMuted,
            text_disabled: textDisabled,
            mapStyle: mapStyle,
            mapTheme: mapTheme
        };
    }

    function updatePreview(theme) {
        const $preview = $('#limo-preview-container .preview-form');
        
        $preview.css({
            'background-color': theme.background,
            'color': theme.text_primary,
            'border': `1px solid ${theme.surface_dark}`
        });
        
        $preview.find('.preview-header').css('color', theme.text_primary);
        $preview.find('.preview-text').css('color', theme.text_secondary);
        
        $preview.find('.preview-button').css({
            'background-color': theme.primary_color,
            'color': isLightColor(theme.primary_color) ? '#000000' : '#ffffff',
            'border': 'none'
        });
        
        $preview.find('.preview-input').css({
            'background-color': theme.surface,
            'border-color': theme.surface_dark,
            'color': theme.text_primary
        });
    }

    function updateHiddenFields(theme) {
        $('#generated_primary_light').val(theme.primary_light);
        $('#generated_primary_dark').val(theme.primary_dark);
        $('#generated_surface').val(theme.surface);
        $('#generated_surface_dark').val(theme.surface_dark);
        $('#generated_text_primary').val(theme.text_primary);
        $('#generated_text_secondary').val(theme.text_secondary);
        $('#generated_text_muted').val(theme.text_muted);
        $('#generated_text_disabled').val(theme.text_disabled);
        $('#generated_map_style').val(theme.mapStyle);
        $('#generated_map_theme').val(theme.mapTheme);
    }

    function updateCustomPreview() {
        const primaryColor = $('#custom_primary_color').val();
        const backgroundColor = $('#custom_background_color').val();
        
        $('.custom-preview-bg').css('background-color', backgroundColor);
        $('.custom-preview-primary').css('background-color', primaryColor);
        
        const theme = generateSmartTheme(primaryColor, backgroundColor);
        updatePreview(theme);
        updateHiddenFields(theme);
    }

    $(document).ready(function() {
        // Handle preset selection
        $('.limo-preset-card').on('click', function() {
            const $card = $(this);
            const preset = $card.data('preset');
            
            // Update radio button
            $card.find('input[type="radio"]').prop('checked', true);
            
            // Update visual selection
            $('.limo-preset-card').removeClass('selected');
            $card.addClass('selected');
            
            // Show/hide custom colors
            if (preset === 'custom') {
                $('.limo-custom-colors').show();
                updateCustomPreview();
            } else {
                $('.limo-custom-colors').hide();
                
                // Generate theme from preset
                const primaryColor = $card.data('primary');
                const backgroundColor = $card.data('background');
                const theme = generateSmartTheme(primaryColor, backgroundColor);
                
                updatePreview(theme);
                updateHiddenFields(theme);
            }
        });

        // Handle custom color changes
        $('#custom_primary_color, #custom_background_color').on('change input', function() {
            updateCustomPreview();
        });

        // Initialize preview on page load
        const selectedPreset = $('.limo-preset-card.selected');
        if (selectedPreset.length) {
            if (selectedPreset.data('preset') === 'custom') {
                updateCustomPreview();
            } else {
                const primaryColor = selectedPreset.data('primary');
                const backgroundColor = selectedPreset.data('background');
                const theme = generateSmartTheme(primaryColor, backgroundColor);
                updatePreview(theme);
                updateHiddenFields(theme);
            }
        }
    });

})(jQuery);
