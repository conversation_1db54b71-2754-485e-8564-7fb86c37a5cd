/**
 * LIMO BOOKING ADMIN CSS - COMMERCIAL WORDPRESS PLUGIN
 *
 * BULLETPROOF CSS SCOPING STRATEGY:
 * 1. All styles prefixed with .limo-booking-admin-wrapper
 * 2. High specificity without !important abuse
 * 3. CSS isolation to prevent theme conflicts
 * 4. Defensive CSS against WordPress theme interference
 */

/* =============================================================================
   CSS ISOLATION & RESET FOR ADMIN AREA
   ============================================================================= */

/* Main admin wrapper - highest specificity for isolation */
.wp-admin .limo-booking-admin-wrapper.limo-booking-admin-wrapper {
    /* CSS Reset for our admin area */
    all: initial;

    /* Re-establish essential properties */
    display: block;
    box-sizing: border-box;
    max-width: 1200px;
    margin: 0;
    padding: 0;

    /* Typography reset */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #1e293b;

    /* Ensure our styles take precedence */
    position: relative;
    z-index: 1;
}

/* Force box-sizing for all child elements */
.wp-admin .limo-booking-admin-wrapper.limo-booking-admin-wrapper *,
.wp-admin .limo-booking-admin-wrapper.limo-booking-admin-wrapper *::before,
.wp-admin .limo-booking-admin-wrapper.limo-booking-admin-wrapper *::after {
    box-sizing: border-box;
}

/* =============================================================================
   ADMIN SECTIONS WITH BULLETPROOF SCOPING
   ============================================================================= */

/* Admin sections - scoped to prevent theme conflicts */
.wp-admin .limo-booking-admin-wrapper .limo-admin-section.limo-admin-section {
    display: block;
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 32px;
    margin: 0 0 32px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    width: 100%;
}

/* Section headings */
.wp-admin .limo-booking-admin-wrapper .limo-admin-section h2.limo-admin-section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0 0 8px 0;
    padding: 0;
    font-size: 24px;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.2;
    border: none;
    background: none;
    text-transform: none;
    letter-spacing: normal;
}

/* Dashicons in headings */
.wp-admin .limo-booking-admin-wrapper .limo-admin-section h2 .dashicons {
    font-size: 28px;
    color: #3b82f6;
    width: 28px;
    height: 28px;
    line-height: 1;
}

/* Section descriptions */
.wp-admin .limo-booking-admin-wrapper .limo-admin-section .description {
    display: block;
    margin: 0 0 24px 0;
    padding: 0;
    color: #64748b;
    font-size: 16px;
    line-height: 1.5;
    font-weight: normal;
}

/* =============================================================================
   FORM ELEMENTS WITH BULLETPROOF SCOPING
   ============================================================================= */

/* Form grid layout */
.wp-admin .limo-booking-admin-wrapper .limo-form-grid.limo-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Form fields */
.wp-admin .limo-booking-admin-wrapper .limo-form-field.limo-form-field {
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    width: 100%;
}

/* Form labels */
.wp-admin .limo-booking-admin-wrapper .limo-form-field label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin: 0 0 8px 0;
    padding: 0;
    font-size: 14px;
    line-height: 1.4;
    cursor: default;
}

/* Text and URL inputs */
.wp-admin .limo-booking-admin-wrapper .limo-form-field input[type="text"],
.wp-admin .limo-booking-admin-wrapper .limo-form-field input[type="url"] {
    display: block;
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.4;
    color: #1e293b;
    background: #ffffff;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    margin: 0;
    font-family: inherit;
}

/* Input focus states */
.wp-admin .limo-booking-admin-wrapper .limo-form-field input[type="text"]:focus,
.wp-admin .limo-booking-admin-wrapper .limo-form-field input[type="url"]:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Field descriptions */
.wp-admin .limo-booking-admin-wrapper .field-description {
    display: block;
    margin: 8px 0 0 0;
    padding: 0;
    color: #6b7280;
    font-size: 13px;
    line-height: 1.4;
    font-weight: normal;
}

.wp-admin .limo-booking-admin-wrapper .field-description a {
    color: #3b82f6;
    text-decoration: none;
}

.wp-admin .limo-booking-admin-wrapper .field-description a:hover {
    text-decoration: underline;
}

/* =============================================================================
   THEME SELECTOR WITH BULLETPROOF SCOPING
   ============================================================================= */

/* Theme selector headings */
.wp-admin .limo-booking-admin-wrapper .limo-theme-selector h3 {
    display: block;
    margin: 0 0 16px 0;
    padding: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.2;
    border: none;
    background: none;
}

/* Preset grid */
.wp-admin .limo-booking-admin-wrapper .limo-preset-grid.limo-preset-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 0 0 32px 0;
    padding: 0;
    width: 100%;
}

/* Preset cards */
.wp-admin .limo-booking-admin-wrapper .limo-preset-card.limo-preset-card {
    position: relative;
    display: block;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    margin: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #ffffff;
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
}

.wp-admin .limo-booking-admin-wrapper .limo-preset-card.limo-preset-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    transform: translateY(-2px);
}

.wp-admin .limo-booking-admin-wrapper .limo-preset-card.limo-preset-card.selected {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

/* Radio buttons in preset cards */
.wp-admin .limo-booking-admin-wrapper .limo-preset-card input[type="radio"] {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 20px;
    height: 20px;
    margin: 0;
    accent-color: #3b82f6;
    cursor: pointer;
}

/* Preset preview containers */
.wp-admin .limo-booking-admin-wrapper .preset-preview {
    display: block;
    margin: 0 0 16px 0;
    padding: 0;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e5e7eb;
    width: 100%;
}

.wp-admin .limo-booking-admin-wrapper .preset-preview img {
    display: block;
    width: 100%;
    height: 80px;
    object-fit: cover;
    border: none;
    margin: 0;
    padding: 0;
}

/* Custom preview elements */
.wp-admin .limo-booking-admin-wrapper .custom-preview-bg {
    display: block;
    width: 100%;
    height: 80px;
    position: relative;
    border-radius: 8px;
    margin: 0;
    padding: 0;
}

.wp-admin .limo-booking-admin-wrapper .custom-preview-primary {
    position: absolute;
    top: 12px;
    left: 12px;
    width: 80px;
    height: 20px;
    border-radius: 4px;
    margin: 0;
    padding: 0;
}

/* Preset info sections */
.wp-admin .limo-booking-admin-wrapper .preset-info h4 {
    display: block;
    margin: 0 0 8px 0;
    padding: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.2;
    border: none;
    background: none;
}

.wp-admin .limo-booking-admin-wrapper .preset-info p {
    display: block;
    margin: 0;
    padding: 0;
    color: #64748b;
    font-size: 14px;
    line-height: 1.4;
    font-weight: normal;
}

/* Custom Colors Section */
.limo-custom-colors {
    margin-top: 32px;
    padding-top: 32px;
    border-top: 1px solid #e5e7eb;
}

.limo-custom-colors h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
}

.limo-color-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
}

.limo-color-field {
    display: flex;
    flex-direction: column;
}

.limo-color-field input[type="color"] {
    width: 100%;
    height: 50px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.limo-color-field input[type="color"]:hover {
    border-color: #3b82f6;
}

/* Live Preview */
.limo-theme-preview {
    margin-top: 32px;
    padding-top: 32px;
    border-top: 1px solid #e5e7eb;
}

.limo-theme-preview h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
}

.preview-container {
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
    background: #f8fafc;
}

.preview-form {
    max-width: 400px;
    margin: 0 auto;
    padding: 24px;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.preview-header {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
}

.preview-button {
    padding: 12px 24px;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.preview-text {
    margin-bottom: 8px;
    font-size: 14px;
}

.preview-input {
    height: 40px;
    border-radius: 6px;
    border: 1px solid;
    margin-bottom: 16px;
}

/* Vehicle Image Upload */
.limo-image-uploader-wrapper {
    margin: 10px 0;
}

.limo-image-preview-wrapper {
    margin-bottom: 10px;
}

.limo-image-preview-wrapper img {
    border: 1px solid #ddd;
    border-radius: 4px;
}

.upload_vehicle_image_button,
.remove_vehicle_image_button {
    margin-right: 10px;
}

/* Responsive */
@media screen and (max-width: 782px) {
    .limo-booking-admin .form-table td {
        padding: 15px 0;
    }

    .limo-booking-preview-container iframe {
        height: 400px;
    }
}