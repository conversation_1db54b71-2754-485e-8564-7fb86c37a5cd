<?php
/**
 * Simplified Settings page template for Limo Booking plugin
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Render API settings section
 */
function render_api_settings() {
    $settings = get_option('limo_booking_settings', array());
    ?>
    <div class="limo-admin-section">
        <h2 class="limo-admin-section-title"><span class="dashicons dashicons-admin-plugins"></span> <?php _e('API Configuration', 'limo-booking'); ?></h2>
        <p class="description"><?php _e('Configure API keys for maps and integrations.', 'limo-booking'); ?></p>

        <div class="limo-form-grid">
            <div class="limo-form-field">
                <label for="mapbox_api_key"><?php _e('Mapbox API Key', 'limo-booking'); ?></label>
                <input type="text"
                       id="mapbox_api_key"
                       name="limo_booking_settings[mapbox_api_key]"
                       value="<?php echo esc_attr($settings['mapbox_api_key'] ?? ''); ?>"
                       class="regular-text"
                       placeholder="pk.eyJ1IjoieW91cnVzZXJuYW1lIi...">
                <p class="field-description">
                    <?php _e('Get your free API key from', 'limo-booking'); ?>
                    <a href="https://account.mapbox.com/" target="_blank">Mapbox</a>
                </p>
            </div>

            <div class="limo-form-field">
                <label for="here_api_key"><?php _e('HERE API Key (Optional)', 'limo-booking'); ?></label>
                <input type="text"
                       id="here_api_key"
                       name="limo_booking_settings[here_api_key]"
                       value="<?php echo esc_attr($settings['here_api_key'] ?? ''); ?>"
                       class="regular-text"
                       placeholder="Your HERE API key">
                <p class="field-description">
                    <?php _e('For enhanced geocoding and routing.', 'limo-booking'); ?>
                </p>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Render simplified theme settings section
 */
function render_theme_settings() {
    $settings = get_option('limo_booking_settings', array());
    $current_preset = $settings['theme_preset'] ?? 'classic_dark';
    $custom_primary = $settings['primary_color'] ?? '#765a3d';
    $custom_background = $settings['background'] ?? '#000000';

    // Theme presets
    $presets = [
        'classic_dark' => [
            'name' => 'Classic Dark',
            'description' => 'Professional dark theme with brown accents',
            'primary' => '#765a3d',
            'background' => '#000000',
            'preview' => 'data:image/svg+xml;base64,' . base64_encode('<svg width="120" height="80" xmlns="http://www.w3.org/2000/svg"><rect width="120" height="80" fill="#000000"/><rect x="10" y="10" width="100" height="20" fill="#765a3d"/><rect x="10" y="40" width="80" height="8" fill="#ffffff"/><rect x="10" y="55" width="60" height="8" fill="#e5e7eb"/></svg>')
        ],
        'modern_light' => [
            'name' => 'Modern Light',
            'description' => 'Clean light theme with blue accents',
            'primary' => '#1565c0',
            'background' => '#ffffff',
            'preview' => 'data:image/svg+xml;base64,' . base64_encode('<svg width="120" height="80" xmlns="http://www.w3.org/2000/svg"><rect width="120" height="80" fill="#ffffff" stroke="#e5e7eb"/><rect x="10" y="10" width="100" height="20" fill="#1565c0"/><rect x="10" y="40" width="80" height="8" fill="#1f2937"/><rect x="10" y="55" width="60" height="8" fill="#6b7280"/></svg>')
        ],
        'elegant_black' => [
            'name' => 'Elegant Black',
            'description' => 'Sophisticated all-black theme',
            'primary' => '#333333',
            'background' => '#121212',
            'preview' => 'data:image/svg+xml;base64,' . base64_encode('<svg width="120" height="80" xmlns="http://www.w3.org/2000/svg"><rect width="120" height="80" fill="#121212"/><rect x="10" y="10" width="100" height="20" fill="#333333"/><rect x="10" y="40" width="80" height="8" fill="#ffffff"/><rect x="10" y="55" width="60" height="8" fill="#e0e0e0"/></svg>')
        ],
        'luxury_gold' => [
            'name' => 'Luxury Gold',
            'description' => 'Premium dark theme with gold accents',
            'primary' => '#d4af37',
            'background' => '#1a1a1a',
            'preview' => 'data:image/svg+xml;base64,' . base64_encode('<svg width="120" height="80" xmlns="http://www.w3.org/2000/svg"><rect width="120" height="80" fill="#1a1a1a"/><rect x="10" y="10" width="100" height="20" fill="#d4af37"/><rect x="10" y="40" width="80" height="8" fill="#ffffff"/><rect x="10" y="55" width="60" height="8" fill="#e5e7eb"/></svg>')
        ],
        'corporate_blue' => [
            'name' => 'Corporate Blue',
            'description' => 'Professional light theme',
            'primary' => '#0066cc',
            'background' => '#f8f9fa',
            'preview' => 'data:image/svg+xml;base64,' . base64_encode('<svg width="120" height="80" xmlns="http://www.w3.org/2000/svg"><rect width="120" height="80" fill="#f8f9fa" stroke="#e5e7eb"/><rect x="10" y="10" width="100" height="20" fill="#0066cc"/><rect x="10" y="40" width="80" height="8" fill="#1f2937"/><rect x="10" y="55" width="60" height="8" fill="#6b7280"/></svg>')
        ]
    ];
    ?>
    <div class="limo-admin-section">
        <h2 class="limo-admin-section-title"><span class="dashicons dashicons-art"></span> <?php _e('Theme & Appearance', 'limo-booking'); ?></h2>
        <p class="description"><?php _e('Choose a theme preset or create your own custom colors. Text colors are automatically optimized for contrast.', 'limo-booking'); ?></p>

        <div class="limo-theme-selector">
            <h3><?php _e('Theme Presets', 'limo-booking'); ?></h3>
            <div class="limo-preset-grid">
                <?php foreach ($presets as $preset_id => $preset): ?>
                    <div class="limo-preset-card <?php echo $current_preset === $preset_id ? 'selected' : ''; ?>"
                         data-preset="<?php echo esc_attr($preset_id); ?>"
                         data-primary="<?php echo esc_attr($preset['primary']); ?>"
                         data-background="<?php echo esc_attr($preset['background']); ?>">
                        <div class="preset-preview">
                            <img src="<?php echo esc_attr($preset['preview']); ?>" alt="<?php echo esc_attr($preset['name']); ?> preview">
                        </div>
                        <div class="preset-info">
                            <h4><?php echo esc_html($preset['name']); ?></h4>
                            <p><?php echo esc_html($preset['description']); ?></p>
                        </div>
                        <input type="radio"
                               name="limo_booking_settings[theme_preset]"
                               value="<?php echo esc_attr($preset_id); ?>"
                               <?php checked($current_preset, $preset_id); ?>>
                    </div>
                <?php endforeach; ?>

                <!-- Custom Theme Option -->
                <div class="limo-preset-card custom-theme <?php echo $current_preset === 'custom' ? 'selected' : ''; ?>"
                     data-preset="custom">
                    <div class="preset-preview custom-preview">
                        <div class="custom-preview-bg" style="background-color: <?php echo esc_attr($custom_background); ?>;">
                            <div class="custom-preview-primary" style="background-color: <?php echo esc_attr($custom_primary); ?>;"></div>
                        </div>
                    </div>
                    <div class="preset-info">
                        <h4><?php _e('Custom Theme', 'limo-booking'); ?></h4>
                        <p><?php _e('Create your own color combination', 'limo-booking'); ?></p>
                    </div>
                    <input type="radio"
                           name="limo_booking_settings[theme_preset]"
                           value="custom"
                           <?php checked($current_preset, 'custom'); ?>>
                </div>
            </div>
        </div>

        <!-- Custom Color Picker (shown when custom is selected) -->
        <div class="limo-custom-colors" style="<?php echo $current_preset !== 'custom' ? 'display: none;' : ''; ?>">
            <h3><?php _e('Custom Colors', 'limo-booking'); ?></h3>
            <div class="limo-color-grid">
                <div class="limo-color-field">
                    <label for="custom_primary_color"><?php _e('Primary Color', 'limo-booking'); ?></label>
                    <input type="color"
                           id="custom_primary_color"
                           name="limo_booking_settings[primary_color]"
                           value="<?php echo esc_attr($custom_primary); ?>"
                           class="limo-color-picker">
                    <p class="field-description"><?php _e('Used for buttons, links, and accents', 'limo-booking'); ?></p>
                </div>

                <div class="limo-color-field">
                    <label for="custom_background_color"><?php _e('Background Color', 'limo-booking'); ?></label>
                    <input type="color"
                           id="custom_background_color"
                           name="limo_booking_settings[background]"
                           value="<?php echo esc_attr($custom_background); ?>"
                           class="limo-color-picker">
                    <p class="field-description"><?php _e('Main background color. Text colors will auto-adjust for contrast.', 'limo-booking'); ?></p>
                </div>
            </div>
        </div>

        <!-- Live Preview -->
        <div class="limo-theme-preview">
            <h3><?php _e('Live Preview', 'limo-booking'); ?></h3>
            <div class="preview-container" id="limo-preview-container">
                <div class="preview-form">
                    <div class="preview-header">Limo Booking Form</div>
                    <div class="preview-button">Book Now</div>
                    <div class="preview-text">Select your pickup location</div>
                    <div class="preview-input"></div>
                </div>
            </div>
        </div>

        <!-- Hidden fields for generated theme values -->
        <input type="hidden" name="limo_booking_settings[primary_light]" id="generated_primary_light" value="">
        <input type="hidden" name="limo_booking_settings[primary_dark]" id="generated_primary_dark" value="">
        <input type="hidden" name="limo_booking_settings[surface]" id="generated_surface" value="">
        <input type="hidden" name="limo_booking_settings[surface_dark]" id="generated_surface_dark" value="">
        <input type="hidden" name="limo_booking_settings[text_primary]" id="generated_text_primary" value="">
        <input type="hidden" name="limo_booking_settings[text_secondary]" id="generated_text_secondary" value="">
        <input type="hidden" name="limo_booking_settings[text_muted]" id="generated_text_muted" value="">
        <input type="hidden" name="limo_booking_settings[text_disabled]" id="generated_text_disabled" value="">
        <input type="hidden" name="limo_booking_settings[mapStyle]" id="generated_map_style" value="">
        <input type="hidden" name="limo_booking_settings[mapTheme]" id="generated_map_theme" value="">
    </div>
    <?php
}

/**
 * Render vehicle settings section
 */
function render_vehicle_settings() {
    $settings = get_option('limo_booking_settings', array());
    $vehicle_types = [
        'sedan' => 'Sedan',
        'suv' => 'SUV',
        'van' => 'Van',
        'stretch_limo' => 'Stretch Limo',
        'party_bus' => 'Party Bus'
    ];
    ?>
    <div class="limo-admin-section">
        <h2 class="limo-admin-section-title"><span class="dashicons dashicons-car"></span> <?php _e('Vehicle Settings', 'limo-booking'); ?></h2>
        <p class="description"><?php _e('Upload images for each vehicle type. These will be displayed in the booking form.', 'limo-booking'); ?></p>

        <div class="limo-form-grid">
            <?php foreach ($vehicle_types as $type_key => $type_name):
                $image_url = $settings['vehicle_images'][$type_key] ?? '';
                $field_id = 'vehicle_image_' . $type_key;
            ?>
                <div class="limo-form-field">
                    <label><?php printf(__('%s Image', 'limo-booking'), $type_name); ?></label>

                    <div class="limo-image-uploader-wrapper">
                        <div id="preview_<?php echo esc_attr($field_id); ?>" class="limo-image-preview-wrapper" style="<?php echo empty($image_url) ? 'display:none;' : ''; ?>">
                            <img src="<?php echo esc_url($image_url); ?>" style="max-width:150px; height:auto; border-radius: 8px;">
                        </div>

                        <input type="hidden"
                               name="limo_booking_settings[vehicle_images][<?php echo esc_attr($type_key); ?>]"
                               id="<?php echo esc_attr($field_id); ?>"
                               value="<?php echo esc_attr($image_url); ?>">

                        <button type="button"
                                class="button upload_vehicle_image_button"
                                data-target-id="<?php echo esc_attr($field_id); ?>"
                                data-preview-id="preview_<?php echo esc_attr($field_id); ?>">
                            <?php printf(__('Upload %s Image', 'limo-booking'), $type_name); ?>
                        </button>

                        <button type="button"
                                class="button remove_vehicle_image_button"
                                data-target-id="<?php echo esc_attr($field_id); ?>"
                                data-preview-id="preview_<?php echo esc_attr($field_id); ?>"
                                style="<?php echo empty($image_url) ? 'display:none;' : ''; ?>">
                            <?php _e('Remove Image', 'limo-booking'); ?>
                        </button>
                    </div>

                    <p class="field-description"><?php printf(__('Upload an image for the %s vehicle type.', 'limo-booking'), strtolower($type_name)); ?></p>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php
}

/**
 * Render the settings page
 */
function render_settings_page() {
    if (!current_user_can('manage_options')) {
        return;
    }

    // Show settings updated message
    if (isset($_GET['settings-updated'])) {
        add_settings_error(
            'limo_booking_messages',
            'limo_booking_message',
            __('Settings Saved', 'limo-booking'),
            'updated'
        );
    }
    ?>
    <div class="wrap">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
        <?php settings_errors('limo_booking_messages'); ?>

        <form method="post" action="options.php">
            <?php
            settings_fields('limo_booking_options');
            render_api_settings();
            render_theme_settings();
            submit_button();
            ?>
        </form>
    </div>
    <?php
}