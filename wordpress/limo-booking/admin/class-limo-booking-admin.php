<?php

class Limo_Booking_Admin {

    /**
     * Option group and option name for WordPress Settings API.
     */
    const SETTINGS_GROUP = 'limo_booking_settings'; // Used for settings_fields()
    const OPTION_NAME = 'limo_booking_settings';    // Used for get_option() and register_setting()

    private $vehicle_types = [
        'sedan' => 'Sedan',
        'suv' => 'SUV',
        'van' => 'Van',
        'stretch_limo' => 'Stretch Limo',
        'party_bus' => 'Party Bus'
    ];

    /**
     * Predefined theme presets with smart auto-contrast
     */
    private $theme_presets = [
        'classic_dark' => [
            'name' => 'Classic Dark',
            'description' => 'Professional dark theme with brown accents',
            'primary_color' => '#765a3d',
            'background' => '#000000',
            'preview_image' => 'classic-dark.png'
        ],
        'modern_light' => [
            'name' => 'Modern Light',
            'description' => 'Clean light theme with blue accents',
            'primary_color' => '#1565c0',
            'background' => '#ffffff',
            'preview_image' => 'modern-light.png'
        ],
        'elegant_black' => [
            'name' => 'Elegant Black',
            'description' => 'Sophisticated all-black theme',
            'primary_color' => '#333333',
            'background' => '#121212',
            'preview_image' => 'elegant-black.png'
        ],
        'luxury_gold' => [
            'name' => 'Luxury Gold',
            'description' => 'Premium dark theme with gold accents',
            'primary_color' => '#d4af37',
            'background' => '#1a1a1a',
            'preview_image' => 'luxury-gold.png'
        ],
        'corporate_blue' => [
            'name' => 'Corporate Blue',
            'description' => 'Professional light theme',
            'primary_color' => '#0066cc',
            'background' => '#f8f9fa',
            'preview_image' => 'corporate-blue.png'
        ]
    ];

    /**
     * Constructor.
     * Hooks into WordPress admin initialization.
     */
    public function __construct() {
        add_action('admin_init', [$this, 'register_admin_settings']);
        add_action('admin_init', [$this, 'register_admin_assets']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        add_action('wp_ajax_limo_booking_preview_theme', [$this, 'ajax_preview_theme']);
        add_action('wp_ajax_limo_booking_save_theme', [$this, 'ajax_save_theme']);
    }

    /**
     * Calculate if a color is light or dark
     */
    private function is_light_color($hex) {
        $hex = ltrim($hex, '#');
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));

        // Calculate brightness using standard formula
        $brightness = ($r * 299 + $g * 587 + $b * 114) / 1000;

        return $brightness > 128;
    }

    /**
     * Generate smart theme colors based on primary and background
     */
    private function generate_smart_theme($primary_color, $background_color) {
        $is_dark_bg = !$this->is_light_color($background_color);
        $is_light_primary = $this->is_light_color($primary_color);

        // Generate primary variations
        $primary_light = $this->lighten_color($primary_color, 15);
        $primary_dark = $this->darken_color($primary_color, 15);

        if ($is_dark_bg) {
            // Dark theme
            $surface = $this->lighten_color($background_color, 8);
            $surface_dark = $this->lighten_color($background_color, 4);
            $text_primary = '#ffffff';
            $text_secondary = '#e5e7eb';
            $text_muted = '#9ca3af';
            $text_disabled = 'rgba(255, 255, 255, 0.5)';
            $map_style = 'mapbox://styles/mapbox/dark-v11';
        } else {
            // Light theme
            $surface = $this->darken_color($background_color, 3);
            $surface_dark = $this->darken_color($background_color, 8);
            $text_primary = '#1f2937';
            $text_secondary = '#374151';
            $text_muted = '#6b7280';
            $text_disabled = 'rgba(31, 41, 55, 0.5)';
            $map_style = 'mapbox://styles/mapbox/light-v11';
        }

        return [
            'primary_color' => $primary_color,
            'primary_light' => $primary_light,
            'primary_dark' => $primary_dark,
            'background' => $background_color,
            'surface' => $surface,
            'surface_dark' => $surface_dark,
            'text_primary' => $text_primary,
            'text_secondary' => $text_secondary,
            'text_muted' => $text_muted,
            'text_disabled' => $text_disabled,
            'mapStyle' => $map_style,
            'mapTheme' => $is_dark_bg ? 'dark' : 'light'
        ];
    }

    /**
     * Lighten a hex color by percentage
     */
    private function lighten_color($hex, $percent) {
        $hex = ltrim($hex, '#');
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));

        $r = min(255, $r + ($percent / 100) * (255 - $r));
        $g = min(255, $g + ($percent / 100) * (255 - $g));
        $b = min(255, $b + ($percent / 100) * (255 - $b));

        return sprintf('#%02x%02x%02x', $r, $g, $b);
    }

    /**
     * Darken a hex color by percentage
     */
    private function darken_color($hex, $percent) {
        $hex = ltrim($hex, '#');
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));

        $r = max(0, $r - ($percent / 100) * $r);
        $g = max(0, $g - ($percent / 100) * $g);
        $b = max(0, $b - ($percent / 100) * $b);

        return sprintf('#%02x%02x%02x', $r, $g, $b);
    }

    /**
     * Register admin assets.
     */
    public function register_admin_assets() {
        wp_register_style(
            'limo-booking-admin',
            LIMO_BOOKING_PLUGIN_URL . 'admin/css/admin.css',
            array(),
            LIMO_BOOKING_VERSION
        );

        wp_register_script(
            'limo-booking-admin',
            LIMO_BOOKING_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery', 'wp-color-picker'),
            LIMO_BOOKING_VERSION,
            true
        );

        // Register theme selector JS
        wp_register_script(
            'limo-booking-theme-selector',
            LIMO_BOOKING_PLUGIN_URL . 'admin/js/theme-selector.js',
            array('jquery'),
            LIMO_BOOKING_VERSION,
            true
        );
    }

    /**
     * Enqueue scripts and styles for the admin page.
     */
    public function enqueue_admin_assets($hook_suffix) {
        // Only load on our admin page. The hook_suffix for a top-level menu page is 'toplevel_page_{menu_slug}'.
        // The menu slug is 'limo-booking' as defined in the main plugin file.
        if ('toplevel_page_limo-booking' !== $hook_suffix) {
            return;
        }

        // Core WordPress assets
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');
        wp_enqueue_media(); // For media uploader

        // Plugin admin assets
        wp_enqueue_style('limo-booking-admin');
        wp_enqueue_script('limo-booking-admin');
        wp_enqueue_script('limo-booking-theme-selector');

        // Custom script for color picker initialization and media uploader
        wp_add_inline_script(
            'limo-booking-admin', // Now hooking to our admin script
            "
            jQuery(document).ready(function($){
                $('.limo-color-picker').wpColorPicker();

                var mediaUploader;

                $(document).on('click', '.upload_vehicle_image_button', function(e) {
                    e.preventDefault();
                    var button = $(this);
                    var targetInputId = button.data('target-id');
                    var previewImageId = button.data('preview-id');

                    if (mediaUploader) {
                        mediaUploader.open();
                        return;
                    }

                    mediaUploader = wp.media.frames.file_frame = wp.media({
                        title: 'Choose Image',
                        button: {
                            text: 'Choose Image'
                        },
                        multiple: false
                    });

                    mediaUploader.on('select', function() {
                        var attachment = mediaUploader.state().get('selection').first().toJSON();
                        $('#' + targetInputId).val(attachment.url);
                        $('#' + previewImageId).attr('src', attachment.url).show();
                        button.siblings('.remove_vehicle_image_button').show();
                    });

                    mediaUploader.open();
                });

                $(document).on('click', '.remove_vehicle_image_button', function(e) {
                    e.preventDefault();
                    var button = $(this);
                    var targetInputId = button.data('target-id');
                    var previewImageId = button.data('preview-id');
                    $('#' + targetInputId).val('');
                    $('#' + previewImageId).attr('src', '').hide();
                    button.hide();
                });
            });
            "
        );
    }

    /**
     * Register all settings, sections, and fields for the admin page.
     */
    public function register_admin_settings() {
        // Register the main setting group and its sanitization callback
        register_setting(
            self::SETTINGS_GROUP,
            self::OPTION_NAME,
            [$this, 'sanitize_limo_settings']
        );

        // API Settings Section
        add_settings_section(
            'limo_booking_api_section',
            __('API Settings', 'limo-booking'),
            [$this, 'render_api_section_callback'],
            'limo_booking_admin_page'
        );

        $api_fields = [
            'salesmate_api_key' => __('Salesmate API Key', 'limo-booking'),
            'here_api_key'      => __('HERE API Key', 'limo-booking'),
            'resend_api_key'    => __('Resend API Key', 'limo-booking'),
            'mapbox_api_key'    => __('Mapbox API Key', 'limo-booking'),
            'webhook_url'       => __('Webhook URL', 'limo-booking'),
        ];
        foreach ($api_fields as $id => $title) {
            add_settings_field($id, $title, [$this, 'render_text_field_callback'], 'limo_booking_admin_page', 'limo_booking_api_section', ['id' => $id, 'type' => ($id === 'webhook_url' ? 'url' : 'text')]);
        }

        // Theme Settings Section
        add_settings_section(
            'limo_booking_theme_section',
            __('Theme Settings', 'limo-booking'),
            [$this, 'render_theme_section_callback'],
            'limo_booking_admin_page'
        );

        $theme_fields = [
            'theme_primary_color' => ['title' => __('Primary Color', 'limo-booking'), 'type' => 'color', 'default' => '#765a3d'],
            'theme_primary_light' => ['title' => __('Primary Light', 'limo-booking'), 'type' => 'color', 'default' => '#8b6d4c'],
            'theme_primary_dark'  => ['title' => __('Primary Dark', 'limo-booking'), 'type' => 'color', 'default' => '#5d472f'],
            'theme_background'    => ['title' => __('Background Color', 'limo-booking'), 'type' => 'color', 'default' => '#000000'],
            'theme_surface'       => ['title' => __('Surface Color', 'limo-booking'), 'type' => 'color', 'default' => '#141414'],
            'theme_surface_dark'  => ['title' => __('Surface Dark Color', 'limo-booking'), 'type' => 'color', 'default' => '#1A1A1A'],
            'theme_text_primary'  => ['title' => __('Text Primary Color', 'limo-booking'), 'type' => 'color', 'default' => '#ffffff'],
            'theme_text_secondary'=> ['title' => __('Text Secondary Color', 'limo-booking'), 'type' => 'color', 'default' => '#e5e7eb'],
            'theme_text_muted'    => ['title' => __('Text Muted Color', 'limo-booking'), 'type' => 'color', 'default' => '#9ca3af'],
            'theme_text_disabled' => ['title' => __('Text Disabled Color (rgba)', 'limo-booking'), 'type' => 'text', 'default' => 'rgba(255, 255, 255, 0.5)'],
            'theme_map_style'     => ['title' => __('Map Style URL', 'limo-booking'), 'type' => 'url', 'default' => 'mapbox://styles/mapbox/dark-v10'],
            'theme_map_theme'     => ['title' => __('Map Theme', 'limo-booking'), 'type' => 'select', 'options' => ['dark' => 'Dark', 'light' => 'Light'], 'default' => 'dark'],
            'hide_theme_selector' => ['title' => __('Hide Theme Selector in Form', 'limo-booking'), 'type' => 'checkbox', 'default' => false, 'value_is_bool' => true],
            'limo_booking_inherit_theme_colors' => ['title' => __('Inherit Parent Theme Colors', 'limo-booking'), 'type' => 'checkbox', 'default' => 'no', 'description' => __('When enabled, the booking form will try to match the parent theme\'s color scheme. (Recommended)', 'limo-booking'), 'value_is_bool' => false, 'checkbox_value' => 'yes'],
        ];

        foreach ($theme_fields as $id => $field_args) {
            $render_callback = 'render_text_field_callback'; // Default
            if ($field_args['type'] === 'color') $render_callback = 'render_color_field_callback';
            elseif ($field_args['type'] === 'checkbox') $render_callback = 'render_checkbox_field_callback';
            elseif ($field_args['type'] === 'select') $render_callback = 'render_select_field_callback';
            add_settings_field($id, $field_args['title'], [$this, $render_callback], 'limo_booking_admin_page', 'limo_booking_theme_section', array_merge(['id' => $id], $field_args));
        }

        // Vehicle Settings Section
        add_settings_section(
            'limo_booking_vehicle_section',
            __('Vehicle Settings', 'limo-booking'),
            [$this, 'render_vehicle_section_callback'],
            'limo_booking_admin_page'
        );

        // Remove placeholder and add actual fields for vehicle images
        // add_settings_field('vehicle_placeholder', __('Vehicle Management', 'limo-booking'), [$this, 'render_vehicle_placeholder_field'], 'limo_booking_admin_page', 'limo_booking_vehicle_section');
        foreach ($this->vehicle_types as $type_key => $type_name) {
            add_settings_field(
                'vehicle_image_' . $type_key, // Field ID
                sprintf(__('Image for %s', 'limo-booking'), $type_name), // Field Title
                [$this, 'render_vehicle_image_upload_field_callback'], // Callback
                'limo_booking_admin_page', // Page
                'limo_booking_vehicle_section', // Section
                [
                    'id' => 'vehicle_image_' . $type_key, // Pass ID for the input
                    'name_key' => $type_key, // Pass the key for the name attribute (sedan, suv, etc.)
                    'vehicle_name' => $type_name // Pass the display name
                ]
            );
        }
    }

    public function render_api_section_callback() { echo '<p>' . esc_html__('Configure API keys for various integrations.', 'limo-booking') . '</p>'; }
    public function render_theme_section_callback() { echo '<p>' . esc_html__('Customize the appearance of the booking form.', 'limo-booking') . '</p>'; }
    public function render_vehicle_section_callback() {
        echo '<p>' . esc_html__('Upload images for each vehicle type. These images will be used in the booking form.', 'limo-booking') . '</p>';
        // Security Note for File Uploads has been moved to the general form security context (options.php handles nonce)
        // Specific JS handling of media uploader is standard WordPress practice.
    }
    // public function render_vehicle_placeholder_field() { echo '<p><em>' . esc_html__('Vehicle image uploads and detailed management will be available here in a future update.', 'limo-booking') . '</em></p>';}

    public function render_vehicle_image_upload_field_callback($args) {
        $options = get_option(self::OPTION_NAME, []);
        $image_key = $args['name_key']; // e.g., 'sedan'
        $image_url = $options['vehicle_images'][$image_key] ?? '';
        $field_id_base = 'vehicle_image_' . $image_key; // e.g., vehicle_image_sedan
        ?>
        <div class="limo-image-uploader-wrapper">
            <div id="preview_<?php echo esc_attr($field_id_base); ?>" class="limo-image-preview-wrapper" style="<?php echo empty($image_url) ? 'display:none;' : ''; ?>">
                <img src="<?php echo esc_url($image_url); ?>" style="max-width:150px; height:auto;">
            </div>
            <input type="hidden"
                   name="<?php echo esc_attr(self::OPTION_NAME); ?>[vehicle_images][<?php echo esc_attr($image_key); ?>]"
                   id="<?php echo esc_attr($field_id_base); ?>"
                   value="<?php echo esc_attr($image_url); ?>">
            <button type="button"
                    class="button upload_vehicle_image_button"
                    data-target-id="<?php echo esc_attr($field_id_base); ?>"
                    data-preview-id="preview_<?php echo esc_attr($field_id_base); ?>">
                <?php printf(esc_html__('Upload %s Image', 'limo-booking'), esc_html($args['vehicle_name'])); ?>
            </button>
            <button type="button"
                    class="button remove_vehicle_image_button"
                    data-target-id="<?php echo esc_attr($field_id_base); ?>"
                    data-preview-id="preview_<?php echo esc_attr($field_id_base); ?>"
                    style="<?php echo empty($image_url) ? 'display:none;' : ''; ?>">
                <?php esc_html_e('Remove Image', 'limo-booking'); ?>
            </button>
        </div>
        <?php
    }

    public function render_text_field_callback($args) {
        $options = get_option(self::OPTION_NAME, []);
        $value = $options[$args['id']] ?? ($args['default'] ?? '');
        $type = $args['type'] ?? 'text';
        printf('<input type="%s" id="%s" name="%s[%s]" value="%s" class="regular-text">', esc_attr($type), esc_attr($args['id']), esc_attr(self::OPTION_NAME), esc_attr($args['id']), esc_attr($value));
        if (!empty($args['description'])) printf('<p class="description">%s</p>', esc_html($args['description']));
    }

    public function render_color_field_callback($args) {
        $options = get_option(self::OPTION_NAME, []);
        $value = $options[$args['id']] ?? ($args['default'] ?? '#000000');
        printf('<input type="text" id="%s" name="%s[%s]" value="%s" class="limo-color-picker">', esc_attr($args['id']), esc_attr(self::OPTION_NAME), esc_attr($args['id']), esc_attr($value));
        if (!empty($args['description'])) printf('<p class="description">%s</p>', esc_html($args['description']));
    }

    public function render_checkbox_field_callback($args) {
        $options = get_option(self::OPTION_NAME, []);
        $id = $args['id'];
        $value_is_bool = $args['value_is_bool'] ?? false; // Does this checkbox store a true/false boolean?
        $checkbox_value_attr = $value_is_bool ? '1' : ($args['checkbox_value'] ?? 'yes'); // The value attribute for the checkbox input itself

        if ($value_is_bool) {
            $current_value = isset($options[$id]) ? (bool)$options[$id] : (bool)($args['default'] ?? false);
        } else {
            $current_value = $options[$id] ?? ($args['default'] ?? 'no');
        }

        $checked_attr = $value_is_bool ? checked($current_value, true, false) : checked($current_value, $checkbox_value_attr, false);

        printf('<label for="%s"><input type="checkbox" id="%s" name="%s[%s]" value="%s" %s> %s</label>', esc_attr($id), esc_attr($id), esc_attr(self::OPTION_NAME), esc_attr($id), esc_attr($checkbox_value_attr), $checked_attr, isset($args['label_for_checkbox']) ? esc_html($args['label_for_checkbox']) : '');
        if (!empty($args['description'])) printf('<p class="description">%s</p>', esc_html($args['description']));
    }

    public function render_select_field_callback($args) {
        $options = get_option(self::OPTION_NAME, []);
        $value = $options[$args['id']] ?? ($args['default'] ?? '');
        printf('<select id="%s" name="%s[%s]">', esc_attr($args['id']), esc_attr(self::OPTION_NAME), esc_attr($args['id']));
        foreach ($args['options'] as $val => $label) {
            printf('<option value="%s" %s>%s</option>', esc_attr($val), selected($value, $val, false), esc_html($label));
        }
        printf('</select>');
        if (!empty($args['description'])) printf('<p class="description">%s</p>', esc_html($args['description']));
    }

    public function sanitize_limo_settings($input) {
        $sanitized = [];
        $current_options = get_option(self::OPTION_NAME, []);

        // Security Note for File Uploads:
        // If this settings array were to store URLs of pre-uploaded files (e.g., vehicle images),
        // the upload process itself must be handled separately with its own nonce and capability checks.
        // This sanitize_limo_settings function would then only be responsible for sanitizing the URL string itself
        // (e.g., using esc_url_raw) if it's part of the $input array.
        // Direct file upload handling (multipart/form-data) should NOT be processed here directly
        // when using settings_fields() and options.php.

        $fields_config = [
            'salesmate_api_key' => ['type' => 'text'], 'here_api_key' => ['type' => 'text'], 'resend_api_key' => ['type' => 'text'], 'mapbox_api_key' => ['type' => 'text'], 'webhook_url' => ['type' => 'url'],
            'theme_primary_color' => ['type' => 'color', 'default' => '#765a3d'], 'theme_primary_light' => ['type' => 'color', 'default' => '#8b6d4c'], 'theme_primary_dark' => ['type' => 'color', 'default' => '#5d472f'],
            'theme_background' => ['type' => 'color', 'default' => '#000000'], 'theme_surface' => ['type' => 'color', 'default' => '#141414'], 'theme_surface_dark' => ['type' => 'color', 'default' => '#1A1A1A'],
            'theme_text_primary' => ['type' => 'color', 'default' => '#ffffff'], 'theme_text_secondary' => ['type' => 'color', 'default' => '#e5e7eb'], 'theme_text_muted' => ['type' => 'color', 'default' => '#9ca3af'],
            'theme_text_disabled' => ['type' => 'rgba_text', 'default' => 'rgba(255, 255, 255, 0.5)'],
            'theme_map_style' => ['type' => 'url', 'default' => 'mapbox://styles/mapbox/dark-v10'],
            'theme_map_theme' => ['type' => 'select', 'options' => ['dark', 'light'], 'default' => 'dark'],
            'hide_theme_selector' => ['type' => 'checkbox_bool', 'default' => false],
            'limo_booking_inherit_theme_colors' => ['type' => 'checkbox_text', 'default' => 'no', 'checkbox_value' => 'yes'],
             // Note: vehicle_images is not part of $fields_config here as it's an array of URLs handled separately.
        ];

        foreach ($fields_config as $key => $config) {
            if (!isset($input[$key]) && $config['type'] !== 'checkbox_bool' && $config['type'] !== 'checkbox_text') { // Checkboxes are handled differently if not set
                 // If a non-checkbox field is not in the input, retain its old value from $current_options or set to default if not present.
                if (isset($current_options[$key])) {
                    $sanitized[$key] = $current_options[$key];
                } else {
                    $sanitized[$key] = $config['default'] ?? '';
                }
                continue;
            }

            $input_value = $input[$key] ?? null; // $input[$key] will exist for checkboxes if checked, or be null

            switch ($config['type']) {
                case 'text': $sanitized[$key] = sanitize_text_field($input_value); break;
                case 'url': $sanitized[$key] = esc_url_raw($input_value); break;
                case 'color':
                    $sanitized_hex = sanitize_hex_color($input_value);
                    $sanitized[$key] = $sanitized_hex ? $sanitized_hex : ($config['default'] ?? '');
                    break;
                case 'rgba_text':
                    if (preg_match('/^rgba\s*\(\s*([01]?\d\d?|2[0-4]\d|25[0-5])\s*,\s*([01]?\d\d?|2[0-4]\d|25[0-5])\s*,\s*([01]?\d\d?|2[0-4]\d|25[0-5])\s*,\s*(0|1|0?\.\d+)\s*\)$/i', $input_value)) {
                        $sanitized[$key] = strtolower(str_replace(' ', '', $input_value));
                    } else {
                        $sanitized[$key] = $config['default'] ?? '';
                    }
                    break;
                case 'select':
                    $sanitized[$key] = in_array($input_value, $config['options']) ? $input_value : ($config['default'] ?? '');
                    break;
                case 'checkbox_bool':
                    $sanitized[$key] = isset($input[$key]); // True if checked (input[$key] exists and is '1'), false if not.
                    break;
                case 'checkbox_text':
                    $checkbox_value_attr = $config['checkbox_value'] ?? 'yes';
                    $sanitized[$key] = (isset($input[$key]) && $input[$key] === $checkbox_value_attr) ? $checkbox_value_attr : 'no';
                    break;
                default: $sanitized[$key] = sanitize_text_field($input_value); break;
            }
        }

        // Sanitize vehicle_images array
        if (isset($input['vehicle_images']) && is_array($input['vehicle_images'])) {
            $sanitized['vehicle_images'] = [];
            foreach ($this->vehicle_types as $type_key => $type_name) { // Loop through defined types to ensure structure
                if (isset($input['vehicle_images'][$type_key])) {
                    $sanitized['vehicle_images'][$type_key] = esc_url_raw($input['vehicle_images'][$type_key]);
                } else {
                    $sanitized['vehicle_images'][$type_key] = ''; // Ensure key exists even if empty
                }
            }
        } else {
            // If not set or not an array, initialize or retain previous valid values.
            // It's important to ensure all expected keys are present if the React side expects them.
            $sanitized['vehicle_images'] = $current_options['vehicle_images'] ?? [];
            // Ensure all defined vehicle types have a key in the array, even if empty.
            foreach ($this->vehicle_types as $type_key => $type_name) {
                if (!isset($sanitized['vehicle_images'][$type_key])) {
                    $sanitized['vehicle_images'][$type_key] = '';
                }
            }
        }

        // Merge with existing options to preserve settings not handled by $fields_config (if any)
        // and to correctly handle the overall structure being saved.
        return array_merge($current_options, $sanitized);
    }

    public function render_settings_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }
        ?>
        <div class="wrap">
            <div class="limo-booking-admin-wrapper">
                <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

                <form method="post" action="options.php">
                    <?php settings_fields(self::SETTINGS_GROUP); ?>

                    <?php
                    // Include the new simplified settings template
                    include_once plugin_dir_path(__FILE__) . 'templates/settings-page.php';

                    // Render API settings
                    render_api_settings();

                    // Render theme settings
                    render_theme_settings();

                    // Render vehicle settings
                    render_vehicle_settings();
                    ?>

                    <div class="limo-admin-section">
                        <?php submit_button(__('Save All Settings', 'limo-booking'), 'primary large'); ?>
                    </div>
                </form>
            </div>
        </div>
        <?php
    }
}
?>
