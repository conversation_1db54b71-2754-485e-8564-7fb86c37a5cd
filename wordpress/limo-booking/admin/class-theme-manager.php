<?php
/**
 * Theme Manager for Limo Booking
 *
 * @package Limo_Booking
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class Theme_Manager
 */
class Theme_Manager {
    /**
     * Instance of this class.
     *
     * @var object
     */
    protected static $instance = null;

    /**
     * Initialize the theme manager
     */
    private function __construct() {
        add_action('wp_ajax_limo_booking_update_theme', array($this, 'ajax_update_theme'));
        add_action('wp_ajax_nopriv_limo_booking_update_theme', array($this, 'ajax_update_theme'));
    }

    /**
     * Return an instance of this class.
     *
     * @return object A single instance of this class.
     */
    public static function get_instance() {
        if (null == self::$instance) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * Get theme configuration
     *
     * @return array Theme configuration
     */
    public function get_theme_config() {
        $settings = get_option('limo_booking_theme_settings', array());
        $scheme_id = isset($settings['theme_scheme']) ? $settings['theme_scheme'] : 'classic_brown';
        
        // Default values
        $defaults = array(
            'primary_color' => '#765a3d',
            'primary_light' => '#8b6d4c',
            'primary_dark' => '#61492f',
            'background' => '#000000',
            'surface' => '#141414',
            'surface_dark' => '#0a0a0a',
            'text_primary' => '#ffffff',
            'text_secondary' => '#e0e0e0',
            'text_muted' => 'rgba(255, 255, 255, 0.7)',
            'text_disabled' => 'rgba(255, 255, 255, 0.5)',
            'mapStyle' => 'mapbox://styles/mapbox/dark-v10',
            'mapTheme' => 'dark',
            'theme_scheme' => 'classic_brown',
            'is_custom_theme' => false
        );

        // Merge saved settings with defaults
        $config = wp_parse_args($settings, $defaults);
        
        // Override with scheme-specific values
        if ($scheme_id !== 'custom') {
            $scheme_values = $this->get_theme_scheme_values($scheme_id);
            if (!empty($scheme_values)) {
                $config = array_merge($config, $scheme_values);
                $config['is_custom_theme'] = false;
            }
        } else {
            $config['is_custom_theme'] = true;
        }
        
        // Ensure scheme ID is set
        $config['theme_scheme'] = $scheme_id;
        
        return $config;
    }
    
    /**
     * Get values for a specific theme scheme
     *
     * @param string $scheme_id Theme scheme ID
     * @return array Theme scheme values
     */
    private function get_theme_scheme_values($scheme_id) {
        $schemes = array(
            'classic_brown' => array(
                'primary_color' => '#765a3d',
                'primary_light' => '#8b6d4c',
                'primary_dark' => '#61492f',
                'background' => '#000000',
                'surface' => '#141414',
                'surface_dark' => '#0a0a0a',
                'text_primary' => '#ffffff',
                'text_secondary' => '#e0e0e0',
                'text_muted' => 'rgba(255, 255, 255, 0.7)',
                'text_disabled' => 'rgba(255, 255, 255, 0.5)',
                'mapStyle' => 'mapbox://styles/mapbox/dark-v10',
                'mapTheme' => 'dark'
            ),
            'modern_blue' => array(
                'primary_color' => '#1565c0',
                'primary_light' => '#1976d2',
                'primary_dark' => '#0d47a1',
                'background' => '#f5f5f5',
                'surface' => '#ffffff',
                'surface_dark' => '#e0e0e0',
                'text_primary' => '#212121',
                'text_secondary' => '#424242',
                'text_muted' => 'rgba(33, 33, 33, 0.7)',
                'text_disabled' => 'rgba(33, 33, 33, 0.5)',
                'mapStyle' => 'mapbox://styles/mapbox/light-v10',
                'mapTheme' => 'light'
            ),
            'elegant_black' => array(
                'primary_color' => '#212121',
                'primary_light' => '#424242',
                'primary_dark' => '#000000',
                'background' => '#121212',
                'surface' => '#1e1e1e',
                'surface_dark' => '#181818',
                'text_primary' => '#ffffff',
                'text_secondary' => '#e0e0e0',
                'text_muted' => 'rgba(255, 255, 255, 0.7)',
                'text_disabled' => 'rgba(255, 255, 255, 0.5)',
                'mapStyle' => 'mapbox://styles/mapbox/dark-v10',
                'mapTheme' => 'dark'
            )
        );
        
        return isset($schemes[$scheme_id]) ? $schemes[$scheme_id] : array();
    }
    
    /**
     * Handle AJAX theme update
     */
    public function ajax_update_theme() {
        // Check nonce for security
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'limo_booking_admin_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
            exit;
        }
        
        // Check for theme data
        if (!isset($_POST['theme']) || !is_array($_POST['theme'])) {
            wp_send_json_error(array('message' => 'No theme data provided'));
            exit;
        }
        
        $theme = $_POST['theme'];
        
        // Get current settings
        $settings = get_option('limo_booking_theme_settings', array());
        
        // Update settings
        $settings['theme_scheme'] = 'custom';
        $settings['primary_color'] = sanitize_hex_color($theme['primary_color']);
        $settings['background_color'] = sanitize_hex_color($theme['background']);
        $settings['surface_color'] = sanitize_hex_color($theme['surface']);
        $settings['text_primary_color'] = sanitize_hex_color($theme['text_primary']);
        $settings['map_theme'] = sanitize_text_field($theme['mapTheme']);
        
        // Save settings
        update_option('limo_booking_theme_settings', $settings);
        
        wp_send_json_success(array(
            'message' => 'Theme updated successfully',
            'theme' => $this->get_theme_config()
        ));
        exit;
    }
}

// Initialize the class
Theme_Manager::get_instance();
