# WordPress Plugin Documentation

## Overview
The WordPress plugin version of the booking form allows for dynamic configuration through the WordPress admin interface. It provides a customizable form for collecting booking quote information that can be integrated with external systems through webhooks.

## Installation

### Requirements
- WordPress 5.8+
- PHP 7.4+
- MySQL 5.7+

### Plugin Installation
1. Upload the plugin ZIP file in WordPress admin
2. Activate the plugin
3. Configure settings in WordPress admin

## Configuration

### Admin Interface
The plugin adds a new menu item "Limo Booking" in WordPress admin with the following sections:

1. **Theme Settings**
   - Color picker for primary colors
   - Color picker for secondary colors
   - Background color settings
   - Surface color settings

2. **API Configuration**
   - Mapbox API key
   - HERE Maps API key
   - Webhook URLs configuration
   - Integration settings (Zapier, n8n, Make.com)

3. **Company Information**
   - Company name
   - Contact email
   - Phone number
   - Business hours

4. **Vehicle Information**
   - Vehicle types and images
   - Vehicle descriptions
   - Capacity settings

### Usage
Add the booking form to any page using:
```
[limo_booking_form]
```

Or in PHP templates:
```php
<?php echo do_shortcode('[limo_booking_form]'); ?>
```

## Integration Options

### Webhook Configuration
The plugin can be configured to send booking data to:
- Custom webhook endpoints
- Zapier webhooks
- n8n workflows
- Make.com scenarios

### Data Format
The booking data is sent as JSON with the following structure:
```json
{
  "service_type": "point-to-point",
  "pickup_location": "123 Main St",
  "dropoff_location": "456 Oak Ave",
  "vehicle_type": "Sedan",
  "date_time": "2023-06-15T14:30:00",
  "contact_info": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "************"
  }
}
```

## Security

### API Key Storage
- API keys are stored securely in WordPress options table
- Keys are encrypted before storage
- Access is restricted to admin users only

### Data Handling
- Form submissions are validated and sanitized
- CSRF protection enabled
- Rate limiting for API requests

## Customization

### Theme Integration
The plugin automatically inherits your WordPress theme's:
- Font family
- Color scheme (optional)
- Responsive breakpoints

### CSS Customization
Custom CSS can be added in:
1. WordPress Customizer
2. Plugin settings page
3. Theme's stylesheet

## Development

### Local Development
```bash
# Build development version
npm run dev:wordpress

# Build production version
npm run build:wordpress
```

### Plugin Structure
```
wp-content/plugins/limo-booking/
├── admin/
│   ├── settings.php
│   └── api-keys.php
├── public/
│   ├── js/
│   └── css/
├── includes/
│   ├── class-limo-booking.php
│   ├── class-webhook-handler.php
│   └── class-data-formatter.php
└── limo-booking.php
```

### Filters and Actions
```php
// Modify form settings
add_filter('limo_booking_form_settings', 'my_custom_settings');

// Modify data before webhook submission
add_filter('limo_booking_webhook_data', 'my_format_webhook_data');

// Hook into booking process
add_action('limo_booking_before_submit', 'my_custom_function');
```

## Troubleshooting

### Common Issues
1. Webhook connection failures
2. Theme conflicts
3. Cache-related issues
4. Map API integration issues

### Debug Mode
Enable debug logging:
```php
define('LIMO_BOOKING_DEBUG', true);
```

### Testing Webhooks
The plugin includes a webhook testing tool in the admin interface that allows you to:
1. Generate test booking data
2. Send test payloads to configured endpoints
3. View webhook response logs

## Updates

### Automatic Updates
The plugin supports WordPress automatic updates.

### Manual Updates
1. Deactivate plugin
2. Upload new version
3. Reactivate plugin
4. Check settings 