# API Documentation

This document provides information about the API services and integrations used in the Limo Booking Application.

## Core API Service (`src/core/services/api.ts`)

The core API service handles data preparation and webhook integrations.

### Key Functions

- **prepareBookingData**: Prepares booking data for external systems
- **sendToWebhook**: Sends data to configured webhook endpoints
- **getVehicles**: Retrieves available vehicle types
- **getLocations**: Retrieves available locations
- **formatDataForCRM**: Formats data for CRM integration
- **validateBookingData**: Validates booking data before submission

## HERE Maps API Integration (`src/core/services/hereApi.ts`)

Integration with HERE Maps API for location services.

### Key Functions

- **geocode**: Converts addresses to coordinates
- **reverseGeocode**: Converts coordinates to addresses
- **calculateRoute**: Calculates routes between locations
- **getEstimatedTravelTime**: Gets estimated travel time between locations
- **getEstimatedDistance**: Gets estimated distance between locations
- **searchPlaces**: Searches for places by name or category

## Mapbox Integration (`src/core/services/mapboxApi.ts`)

Integration with Mapbox for maps and geocoding.

### Key Functions

- **initializeMap**: Initializes a Mapbox map
- **addMarker**: Adds a marker to the map
- **geocode**: Converts addresses to coordinates
- **reverseGeocode**: Converts coordinates to addresses
- **getDirections**: Gets directions between locations
- **searchPlaces**: Searches for places by name or category

## Theme Service (`src/core/services/themeService.ts`)

Service for theme management.

### Key Functions

- **getThemes**: Gets available themes
- **applyTheme**: Applies a theme to the application
- **createTheme**: Creates a new theme
- **updateTheme**: Updates an existing theme
- **deleteTheme**: Deletes a theme

## Webhook Integration (`src/core/services/webhookService.ts`)

Service for integrating with external systems via webhooks.

### Key Functions

- **sendToGenericWebhook**: Sends data to a configured webhook URL
- **sendToZapier**: Formats and sends data to Zapier
- **sendToN8N**: Formats and sends data to n8n
- **sendToMakeCom**: Formats and sends data to Make.com
- **prepareDataForSalesmate**: Prepares data for Salesmate CRM integration

## Diagnostics Service (`src/core/services/diagnostics.ts`)

Diagnostic services for debugging.

### Key Functions

- **logError**: Logs an error
- **logWarning**: Logs a warning
- **logInfo**: Logs information
- **getSystemInfo**: Gets system information
- **testConnection**: Tests connection to external services
- **checkApiStatus**: Checks the status of integrated APIs

## Environment Variables

The application uses the following environment variables for API configuration:

- **VITE_WEBHOOK_URL**: The URL for generic webhook integration
- **VITE_HERE_API_KEY**: The API key for HERE Maps
- **VITE_MAPBOX_ACCESS_TOKEN**: The access token for Mapbox
- **VITE_ZAPIER_WEBHOOK_URL**: Zapier webhook URL (if applicable)
- **VITE_N8N_WEBHOOK_URL**: n8n webhook URL (if applicable)
- **VITE_MAKE_WEBHOOK_URL**: Make.com webhook URL (if applicable)

## Data Structures

### Booking Request Data Structure

```json
{
  "service_type": "point-to-point|hourly|round-trip",
  "pickup": {
    "address": "123 Main St, City, State",
    "coordinates": [longitude, latitude],
    "time": "2023-05-01T14:30:00Z"
  },
  "dropoff": {
    "address": "456 Oak St, City, State",
    "coordinates": [longitude, latitude]
  },
  "vehicle_type": "sedan|suv|van|stretch",
  "duration_hours": 3, // For hourly bookings
  "passengers": 4,
  "contact_info": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890"
  },
  "special_requests": "Child seat required"
}
```

## Webhook Integration

The system can send booking data to the following webhook services:

- **Generic Webhook**: Any HTTP endpoint that accepts JSON
- **Zapier**: For integration with multiple apps through Zapier
- **n8n**: For workflow automation
- **Make.com**: For complex integrations and automations

Example webhook payload:

```json
{
  "event_type": "booking_request",
  "timestamp": "2023-05-01T14:35:22Z",
  "booking_data": {
    // Booking request data structure
  },
  "metadata": {
    "source": "website|wordpress",
    "form_version": "2.0"
  }
}
```

## Error Handling

The API service includes comprehensive error handling with the following error codes:

- **400**: Bad Request - Invalid data format
- **401**: Unauthorized - Invalid API key
- **403**: Forbidden - Access denied
- **404**: Not Found - Resource not found
- **422**: Unprocessable Entity - Valid format but invalid data
- **500**: Internal Server Error - Server-side error

## Authentication

For secure webhook integration, authentication tokens can be included in the Authorization header of webhook requests.

## Testing

The application includes a test mode for validating webhook integrations without submitting actual booking requests. 