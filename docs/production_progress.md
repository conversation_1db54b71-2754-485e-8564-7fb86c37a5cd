Steps for Finalizing Production-Ready Standalone & WordPress Plugin
1. Standalone React App
Checklist:
[ ] Confirm all booking flows work end-to-end (service selection, vehicle, map, date/time, customer info, confirmation).
[ ] Ensure all API integrations (Mapbox/HERE Maps) are working with production credentials.
[ ] Verify webhook data preparation and formatting.
[ ] Test theme customization and responsiveness on all major devices/browsers.
[ ] Run a full accessibility and performance audit.
[ ] Build and test the production bundle (npm run build:standalone).
[ ] Prepare embedding instructions and a sample HTML snippet for clients.
2. WordPress Plugin
Checklist:
[ ] Ensure plugin activation, settings, and shortcode embedding work on a clean WordPress install.
[ ] Test all admin settings (API keys, webhook URLs, theme, company info) and verify they persist and apply correctly.
[ ] Confirm the booking form inherits the WordPress theme where appropriate, but maintains its own design integrity.
[ ] Test webhook integrations (generic endpoints, Zapier, n8n, Make.com) in the WordPress context.
[ ] Validate security (nonce checks, capability checks, sanitization).
[ ] Test the webhook testing tools in the admin interface.
[ ] Build the production plugin (npm run build:wordpress), zip, and test install on a fresh WP site.
[ ] Prepare a final deployment/upgrade guide for clients.
3. General
[ ] Ensure error boundaries and user feedback are in place for all async/API operations.
[ ] Confirm all environment variables are documented and required values are set for production.
[ ] Implement and test the JSON output format for various integration scenarios.
[ ] Document the webhook integration process for different platforms (Zapier, n8n, Make.com).
[ ] Remove any remaining dev/test code or logging.
[ ] Tag a release in git and update version numbers as needed.