/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: 'var(--primary)',
        'primary-light': 'var(--primary-light)',
        'primary-dark': 'var(--primary-dark)',
        'primary-hover': 'var(--primary-hover)',
        'primary-active': 'var(--primary-active)',
        'primary-transparent': 'var(--primary-transparent)',
        background: 'var(--background)',
        surface: 'var(--surface)',
        'surface-dark': 'var(--surface-dark)',
        'surface-light': 'var(--surface-light)',
        'theme-primary': 'var(--text-primary)',
        'theme-secondary': 'var(--text-secondary)',
        'theme-muted': 'var(--text-muted)',
        'theme-disabled': 'var(--text-disabled)',
        'border': 'var(--border)',
        'border-hover': 'var(--border-hover)',
        'success': 'var(--success)',
        'error': 'var(--error)',
        'warning': 'var(--warning)',
        white: 'var(--text-primary)',
        neutral: {
          400: 'var(--text-muted)',
          500: 'var(--text-disabled)',
          800: '#262626',
          900: '#171717',
        },
      },
      opacity: {
        '10': '0.1',
        '20': '0.2',
        '30': '0.3',
        '40': '0.4',
        '50': '0.5',
        '60': '0.6',
        '70': '0.7',
      },
      keyframes: {
        'glow-pulse': {
          '0%, 100%': {
            boxShadow: '0 0 0 0 rgba(118, 90, 61, 0.7)',
            backgroundColor: 'rgba(118, 90, 61, 0.1)',
          },
          '50%': {
            boxShadow: '0 0 30px 0 rgba(118, 90, 61, 0.9)',
            backgroundColor: 'rgba(118, 90, 61, 0.2)',
          },
        },
        'shimmer': {
          '0%': {
            transform: 'translateX(-100%)',
          },
          '100%': {
            transform: 'translateX(100%)',
          },
        },
        'ping': {
          '0%': {
            transform: 'scale(1)',
            opacity: '1',
          },
          '75%, 100%': {
            transform: 'scale(2)',
            opacity: '0',
          },
        },
      },
      animation: {
        'glow-pulse': 'glow-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'shimmer': 'shimmer 2s infinite',
        'ping': 'ping 2s cubic-bezier(0, 0, 0.2, 1) infinite',
      },
    },
  },
  plugins: [],
  safelist: [
    {
      pattern: /^border-white\/\d+$/,
    },
    'animate-glow-pulse',
    'animate-shimmer',
    'animate-ping',
    'updating-header',
    'sticky-header',
  ],
}
