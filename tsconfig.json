{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "noImplicitAny": false, "strictNullChecks": false, "baseUrl": ".", "allowSyntheticDefaultImports": true, "paths": {"@/*": ["src/*"], "@core/*": ["src/core/*"], "@standalone/*": ["src/standalone/*"], "@wordpress/*": ["src/wordpress/*"]}, "types": ["node", "@types/mapbox__mapbox-gl-geocoder"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts"], "exclude": ["node_modules", "config"], "references": [{"path": "./tsconfig.node.json"}]}