// Limo Form Theme Management Rules
// These rules help maintain consistency in theme implementation

// Ensure theme service is used
pattern {
  target: "src/**/*.{ts,tsx}",
  find: "document\\.documentElement\\.style\\.setProperty\\('--",
  avoid: true,
  message: "Don't manipulate CSS variables directly. Use themeService.applyTheme() instead."
}

// Ensure proper theme state access
pattern {
  target: "src/**/*.tsx",
  find: "useThemeStore|themeService",
  ensure: true,
  message: "Use the theme store or service for theme-related operations."
}

// Color palette enforcement
pattern {
  target: "src/**/*.tsx",
  find: "rgba\\(|rgb\\(|#[0-9a-fA-F]{3,6}",
  avoid: true,
  message: "Avoid hardcoded colors. Use Tailwind's theme system or CSS variables for consistent theming."
}

// CSS variable naming conventions
pattern {
  target: "src/core/styles/**/*.css",
  find: "--(?!color-|font-|spacing-|size-|transition-|z-|bg-|border-|shadow-|opacity-)",
  avoid: true,
  message: "CSS variable names should follow the convention: --category-name-variant"
}

// WordPress theme compatibility
pattern {
  target: "src/wordpress/**/*.tsx",
  find: "dispatchThemeChangeEvent",
  ensure: true,
  message: "When changing theme in WordPress context, ensure theme events are dispatched for WordPress integration."
}

// Theme checking pattern
pattern {
  target: "src/core/components/**/*.tsx",
  find: "theme(Store|Service)\\.get(Color|Font|Background)",
  ensure: true,
  message: "Use theme getter methods for accessing theme properties."
}

// Responsive theme application
pattern {
  target: "src/core/styles/**/*.css",
  find: "@media",
  ensure: true,
  message: "Ensure styles have responsive media queries for different device sizes."
}

// Theme validation pattern
pattern {
  target: "src/core/services/themeService.ts",
  find: "validateTheme",
  ensure: true,
  message: "Theme service should validate theme objects before applying them."
}

// CSS-in-JS avoidance
pattern {
  target: "src/**/*.tsx",
  find: "style=\\{\\{",
  avoid: true,
  message: "Avoid inline styles. Use Tailwind classes or apply classes that use CSS variables."
}

// Theme structure consistency
pattern {
  target: "src/**/themeStore.ts",
  find: "(color|background|font):\\s*\\{",
  ensure: true,
  message: "Theme state should have consistent structure with color, background, and font properties."
}

// Theme documentation pattern
pattern {
  target: "src/core/styles/**/*.css",
  find: "/\\*\\s*Theme",
  ensure: true,
  message: "Theme-related CSS should be properly documented with comments."
} 