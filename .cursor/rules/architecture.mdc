// Limo Form Architecture Rules
// These rules help maintain the project's architectural integrity

// Enforce imports from correct locations
import.from {
  target: "src/core/**/*.{ts,tsx}",
  disallow: "src/standalone/**/*",
  message: "Core modules should not import from standalone code. Core should be standalone-agnostic."
}

import.from {
  target: "src/core/**/*.{ts,tsx}",
  disallow: "src/wordpress/**/*",
  message: "Core modules should not import from WordPress-specific code. Core should be platform-agnostic."
}

// Component structure rules
component.jsx {
  target: "src/**/*.tsx",
  ensure: "export default",
  message: "Components should be exported as default exports for consistency."
}

// Store pattern enforcement
pattern {
  target: "src/**/store/*.ts",
  find: "create(\\w+)Store",
  ensure: true,
  message: "Store files should export a store creator function that follows the createXStore naming pattern."
}

pattern {
  target: "src/**/store/*.ts",
  find: "use(\\w+)Store",
  ensure: true,
  message: "Store files should export a hook that follows the useXStore naming pattern."
}

// Service pattern enforcement
pattern {
  target: "src/core/services/*.ts",
  find: "class (\\w+)Service|const (\\w+)Service",
  ensure: true,
  message: "Services should be defined as classes or constants with Service suffix."
}

// Style enforcement for tailwind usage
pattern {
  target: "src/**/*.tsx",
  find: "className=\"[^\"]*\"",
  ensure: true,
  message: "Components should use Tailwind CSS classes for styling."
}

// Ensure proper theme integration
pattern {
  target: "src/core/components/ui/**/*.tsx",
  find: "(bg|text|border)-(primary|secondary)",
  message: "UI components should use theme-aware color classes for styling."
}

// Path aliasing enforcement
pattern {
  target: "src/**/*.{ts,tsx}",
  find: "import .* from '(\\.\\./)+'",
  message: "Use path aliases (@core, @standalone, @wordpress) instead of relative paths for cleaner imports."
}

// WordPress compatibility
pattern {
  target: "src/wordpress/**/*.{ts,tsx}",
  find: "window\\.limoBookingConfig",
  ensure: true,
  message: "WordPress components should properly check for and use the WordPress configuration object."
}

// Diagnostic/Debug utilities
pattern {
  target: "src/**/*.{ts,tsx}",
  find: "console\\.(log|warn|error)",
  avoid: true,
  message: "Use the bookingDebugger for logging instead of console.log for better debugging capabilities."
}

// Form state management best practices
pattern {
  target: "src/**/components/Forms/**/*.tsx",
  find: "useForm|useController",
  ensure: true,
  message: "Form components should use React Hook Form for form state management."
}

// Validation best practices
pattern {
  target: "src/**/components/Forms/**/*.tsx",
  find: "zod|z\\.",
  ensure: true,
  message: "Forms should use Zod for validation schema definition."
}

// Environmental awareness
pattern {
  target: "src/**/*.{ts,tsx}",
  find: "import\\.meta\\.env",
  message: "Be careful with environment variables - consider using a configuration service for better testability."
}

// Performance considerations
pattern {
  target: "src/**/*.tsx",
  find: "React\\.memo|useCallback|useMemo",
  message: "Consider using memoization for complex components or calculations."
} 