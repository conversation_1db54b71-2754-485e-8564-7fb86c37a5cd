import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// WordPress plugin directory path
const WP_PLUGIN_DIR = path.resolve(__dirname, '../wordpress/limo-booking/public');

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  root: path.resolve(__dirname, '..'),
  publicDir: 'public',
  resolve: {
    alias: {
      '@core': path.resolve(__dirname, '../src/core'),
      '@components': path.resolve(__dirname, '../src/components'),
      '@utils': path.resolve(__dirname, '../src/utils')
    },
  },
  build: {
    outDir: path.resolve(__dirname, '../wordpress/limo-booking/public/assets'),
    emptyOutDir: true,
    manifest: true,
    rollupOptions: {
      input: {
        app: path.resolve(__dirname, '../src/wordpress/init-wp.ts')
      }
    }
  },
  server: {
    port: 5173,
    strictPort: true,
    open: true,
    hmr: {
      overlay: false // Disable the error overlay
    }
  }
});

