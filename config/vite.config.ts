import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// Base configuration to be extended by standalone and WordPress configs
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@core': path.resolve(__dirname, '../src/core'),
      '@components': path.resolve(__dirname, '../src/components'),
      '@utils': path.resolve(__dirname, '../src/utils')
    },
  },
  build: {
    sourcemap: true,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: false,
        drop_debugger: true
      }
    }
  },
  envDir: path.resolve(__dirname, '..'),
  envPrefix: 'VITE_',
  server: {
    port: 5173,
    strictPort: true,
    host: true
  }
})
