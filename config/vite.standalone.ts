import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// Get absolute paths
const rootDir = path.resolve(__dirname, '..')

// Load environment variables
const env = loadEnv('', process.cwd())
const resendApiKey = env.VITE_RESEND_API_KEY || 're_d5Y7Q15X_4BJwKTvYZRXVnxjjC66s4WmV'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@core': path.resolve(rootDir, 'src/core'),
      '@assets': path.resolve(rootDir, 'src/assets'),
      '@standalone': path.resolve(rootDir, 'src/standalone'),
      '@components': path.resolve(rootDir, 'src/components'),
      '@utils': path.resolve(rootDir, 'src/utils')
    }
  },
  css: {
    modules: {
      localsConvention: 'camelCase',
      scopeBehaviour: 'local'
    }
  },
  build: {
    outDir: 'dist/standalone',
    emptyOutDir: true,
    sourcemap: true,
    manifest: true,
    target: 'es2015',
    minify: 'terser',
    cssCodeSplit: false,
    terserOptions: {
      compress: {
        drop_console: false,
        drop_debugger: false,
        pure_funcs: []
      },
      format: {
        comments: true
      }
    },
    rollupOptions: {
      input: path.resolve(rootDir, 'src/standalone/main.tsx'),
      external: ['react', 'react-dom', 'mapbox-gl'],
      output: {
        format: 'iife',
        entryFileNames: '[name].js',
        chunkFileNames: '[name].[hash].js',
        assetFileNames: (assetInfo) => {
          if (assetInfo.name === 'style.css') return 'style.css'
          return '[name][extname]'
        },
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
          'mapbox-gl': 'mapboxgl'
        },
        inlineDynamicImports: true
      }
    }
  },
  server: {
    proxy: {
      '/api/proxy/contact': {
        target: 'https://wwlimo.salesmate.io/apis/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/proxy\//, ''),
        headers: {
          'x-linkname': 'wwlimo.salesmate.io',
          'Content-Type': 'application/json',
        }
      },
      '/api/proxy/deal': {
        target: 'https://wwlimo.salesmate.io/apis/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/proxy\//, ''),
        headers: {
          'x-linkname': 'wwlimo.salesmate.io',
          'Content-Type': 'application/json',
        }
      },
      '/api/proxy/v8/routes': {
        target: 'https://router.hereapi.com',
        changeOrigin: true,
        rewrite: (path) => {
          // Extract the query parameters
          const url = new URL(path, 'http://localhost');
          const queryParams = url.searchParams;
          
          // Add the API key to the query parameters
          queryParams.append('apiKey', process.env.VITE_HERE_API_KEY || '');
          
          // Ensure required parameters are present
          if (!queryParams.has('routingMode')) {
            queryParams.append('routingMode', 'fast');
          }
          
          if (!queryParams.has('transportMode')) {
            queryParams.append('transportMode', 'car');
          }
          
          // Return the rewritten path with the API key and required parameters
          return `/v8/routes?${queryParams.toString()}`;
        },
      },
      '/api/proxy/here': {
        target: 'https://router.hereapi.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/proxy\/here/, ''),
      },
      '/api/proxy/email': {
        target: 'https://api.resend.com',
        changeOrigin: true,
        rewrite: (path) => {
          if (path === '/api/proxy/email/send') {
            return '/emails';
          }
          return path.replace(/^\/api\/proxy\/email/, '');
        },
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${resendApiKey}`
        }
      }
    }
  },
  base: '/',
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
    'process.env.VITE_STANDALONE': JSON.stringify(true)
  }
}) 