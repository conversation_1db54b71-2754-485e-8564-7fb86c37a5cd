import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// Get absolute paths
const rootDir = path.resolve(__dirname, '..')
const outDir = path.resolve(rootDir, 'wordpress/limo-booking/public')

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@core': path.resolve(rootDir, 'src/core'),
      '@assets': path.resolve(rootDir, 'src/assets')
    },
  },
  css: {
    modules: {
      localsConvention: 'camelCase',
      scopeBehaviour: 'local',
      // Add a custom prefix for WordPress integration to avoid conflicts
      generateScopedName: 'limo-[local]_[hash:base64:5]'
    }
  },
  build: {
    outDir,
    emptyOutDir: false, // Don't empty the output directory as it's shared with the main build
    sourcemap: true,
    minify: 'terser', // Use terser for better minification
    lib: {
      entry: path.resolve(rootDir, 'src/core/wordpress/LimoBookingWrapper.jsx'),
      name: '<PERSON>oBookingWrapper',
      fileName: 'limo-booking-wrapper',
      formats: ['umd'] // Use UMD format for WordPress compatibility
    },
    rollupOptions: {
      external: ['react', 'react-dom'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM'
        },
        // Ensure CSS is extracted to a separate file
        assetFileNames: 'assets/[name].[ext]',
        // Ensure proper chunk naming
        chunkFileNames: 'chunks/[name]-[hash].js',
        // Ensure proper entry point naming
        entryFileNames: '[name].js',
        // Ensure proper code splitting
        manualChunks: undefined
      }
    }
  },
  base: '/wp-content/plugins/limo-booking/public/',
  define: {
    'process.env.APP_VERSION': JSON.stringify('2.0.0'),
    'process.env.BUILD_DATE': JSON.stringify(new Date().toISOString()),
    'process.env.IS_WORDPRESS': JSON.stringify(true)
  }
})