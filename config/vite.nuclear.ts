import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@core': path.resolve(__dirname, '../src/core'),
      '@standalone': path.resolve(__dirname, '../src/standalone'),
      '@wordpress': path.resolve(__dirname, '../src/wordpress'),
    },
  },
  build: {
    outDir: 'wordpress/limo-booking/public',
    emptyOutDir: false,
    rollupOptions: {
      input: 'src/wordpress/nuclear-minimal.tsx',
      output: {
        entryFileNames: 'nuclear.js',
        chunkFileNames: 'nuclear-[name].js',
        assetFileNames: 'nuclear.[ext]',
        format: 'iife',
        name: 'LimoBookingNuclear'
      },
      external: [],
    },
    target: 'es2015',
    minify: 'terser',
    sourcemap: true,
  },
  define: {
    'process.env.NODE_ENV': '"production"',
  },
  css: {
    postcss: {
      plugins: [],
    },
  },
});
