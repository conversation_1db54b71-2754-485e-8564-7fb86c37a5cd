import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { visualizer } from 'rollup-plugin-visualizer'
import path from 'path'

// Version information
const APP_VERSION = '2.0.0'
const BUILD_DATE = new Date().toISOString()

// Get absolute paths
const rootDir = path.resolve(__dirname, '..')
const outDir = path.resolve(rootDir, 'wordpress/limo-booking/public')

export default defineConfig({
  plugins: [
    react(),
    visualizer({
      filename: path.resolve(outDir, 'stats.html'),
      open: true, // Automatically open in browser
      gzipSize: true,
      brotliSize: true,
    }),
  ],
  resolve: {
    alias: {
      // Remove the mapbox-gl alias to allow proper CSS imports
      '@core': path.resolve(rootDir, 'src/core'),
      '@assets': path.resolve(rootDir, 'src/assets'),
      '@wordpress': path.resolve(rootDir, 'src/wordpress')
    },
  },
  css: {
    postcss: path.resolve(rootDir, 'postcss.config.js'),
    modules: {
      localsConvention: 'camelCase',
      scopeBehaviour: 'local'
    }
  },
  build: {
    outDir,
    emptyOutDir: true,
    sourcemap: true,
    rollupOptions: {
      input: path.resolve(rootDir, 'src/wordpress/index.tsx'),
      external: ['react', 'react-dom'], 
      output: {
        format: 'iife',
        entryFileNames: 'wordpress.js',
        chunkFileNames: 'chunks/[name]-[hash].js',
        // Output CSS directly as wordpress.css, and other assets to assets/ dir
        assetFileNames: (assetInfo) => {
          // If it's a CSS file, always name it wordpress.css
          if (assetInfo.name && assetInfo.name.endsWith('.css')) {
            return 'wordpress.css';
          }
          // For all other assets, use the standard pattern
          return 'assets/[name]-[hash][extname]';
        },
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM'
        }
      }
    },
    // Add explicit CSS handling to ensure it's built correctly
    cssCodeSplit: false, // Don't split CSS, keep it in a single file
    cssMinify: true, // Minify the CSS
  },
  base: '/wp-content/plugins/limo-booking/public/',
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
    'process.env.VITE_WP_ENV': JSON.stringify(true),
    'process.env.VITE_APP_VERSION': JSON.stringify(APP_VERSION),
    'process.env.VITE_BUILD_DATE': JSON.stringify(BUILD_DATE)
  }
})