import safeParser from 'postcss-safe-parser';

export default {
  plugins: {
    '@tailwindcss/nesting': {},
    tailwindcss: {},
    autoprefixer: {},
    'postcss-preset-mantine': {},
    'postcss-simple-vars': {
      variables: {
        'mantine-breakpoint-xs': '36em',
        'mantine-breakpoint-sm': '48em',
        'mantine-breakpoint-md': '62em',
        'mantine-breakpoint-lg': '75em',
        'mantine-breakpoint-xl': '88em',
      },
    },
  },
  // Add a dummy parser function to ensure 'from' option is passed
  parser: (css, opts) => {
    // This ensures the 'from' option is available for all plugins
    return safeParser(css, opts);
  }
}
