{"name": "limo-booking", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:standalone": "vite --config config/vite.standalone.ts", "dev:wordpress": "vite --config config/vite.wordpress.ts", "build": "tsc && vite build", "build:standalone": "tsc && vite build --config config/vite.standalone.ts", "build:standalone:no-types": "vite build --config config/vite.standalone.ts", "build:wordpress": "tsc && vite build --config config/vite.wordpress.ts", "build:nuclear": "tsc && vite build --config config/vite.nuclear.ts", "build:wordpress:no-types": "vite build --config config/vite.wordpress.ts", "build:wp-integration": "node scripts/build-plugin.mjs", "verify-build": "node scripts/verify-build.mjs", "build:wp-plugin": "npm run build:wordpress && npm run build:wp-integration && npm run verify-build", "preview": "vite preview", "preview:standalone": "vite preview --config config/vite.standalone.ts", "preview:wordpress": "vite preview --config config/vite.wordpress.ts", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "wp:dev": "vite --config vite.wordpress.config.ts", "wp:build": "tsc && vite build --config vite.wordpress.config.ts", "wp:preview": "vite preview --config vite.wordpress.config.ts"}, "dependencies": {"@floating-ui/react": "^0.26.9", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@mantine/core": "^8.0.1", "@mantine/hooks": "^8.0.1", "@mapbox/mapbox-gl-geocoder": "^5.0.3", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-select": "^2.1.2", "@types/canvas-confetti": "^1.6.4", "@types/mapbox-gl": "^3.4.1", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-datepicker": "^6.2.0", "autoprefixer": "^10.4.20", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "framer-motion": "^11.11.17", "mapbox-gl": "^2.15.0", "mapbox-gl-geocoder": "^2.0.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-datepicker": "^7.6.0", "react-dom": "^18.3.1", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.28.0", "tailwind-merge": "^2.5.5", "tailwindcss": "^3.4.15", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.13.0", "@tailwindcss/nesting": "^0.0.0-insiders.565cd3e", "@types/mapbox__mapbox-gl-geocoder": "^5.0.0", "@types/node": "^22.9.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "fs-extra": "^11.3.0", "globals": "^15.11.0", "postcss": "^8.5.3", "postcss-nested": "^7.0.2", "postcss-preset-mantine": "^1.17.0", "postcss-safe-parser": "^7.0.1", "postcss-simple-vars": "^7.0.1", "rollup-plugin-visualizer": "^5.14.0", "terser": "^5.39.0", "ts-node": "^10.9.2", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10"}}