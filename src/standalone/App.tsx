import React, { useEffect } from 'react';
import { themeService } from '@core/services/themeService';
import { BookingForm } from '@core/components/BookingForm';
import { BookingType } from '@core/store/bookingStore';
import { ErrorBoundary } from 'react-error-boundary';

interface AppProps {
  formType?: BookingType;
  config?: {
    ajaxUrl: string;
    restUrl: string;
    nonce: string;
    theme: {
      primary_color: string;
      background_color: string;
      surface_color: string;
    };
  };
}

const ErrorFallback = ({ error }: { error: Error }) => {
  console.error('App Error:', error);
  return (
    <div className="error-container p-4 bg-red-500/10 rounded-lg">
      <h2 className="text-xl font-semibold text-red-500 mb-2">Something went wrong</h2>
      <pre className="text-sm text-red-400 whitespace-pre-wrap">
        {error.message}
      </pre>
      <button
        onClick={() => window.location.reload()}
        className="mt-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
      >
        Reload Page
      </button>
    </div>
  );
};

export const App: React.FC<AppProps> = ({ formType = 'point-to-point', config }) => {
  useEffect(() => {
    console.log('App mounted, initializing theme...', {
      hasConfig: !!config,
      formType,
      timestamp: new Date().toISOString()
    });

    try {
      // Apply theme from WordPress config if available
      if (config?.theme) {
        console.log('Applying WordPress theme:', config.theme);
        const theme = {
          color: {
            base: config.theme.primary_color,
            light: config.theme.primary_color, // TODO: Calculate lighter shade
            dark: config.theme.primary_color, // TODO: Calculate darker shade
          },
          background: {
            main: config.theme.background_color,
            surface: config.theme.surface_color,
            surfaceDark: config.theme.surface_color, // TODO: Calculate darker shade
          },
          font: {
            primary: '#ffffff',
            secondary: '#c0af9c',
            muted: '#d5d8dc',
            disabled: 'rgba(255, 255, 255, 0.5)'
          }
        };
        themeService.saveTheme(theme);
        console.log('WordPress theme applied successfully');
      } else {
        console.log('No WordPress config, checking for saved theme...');
        // Load and apply saved theme on initial render
        const savedTheme = themeService.loadTheme();

        // If no saved theme, apply default theme
        if (!savedTheme) {
          console.log('No saved theme found, applying default theme...');
          const defaultTheme = {
            color: {
              base: '#765a3d',
              light: '#8b6d4c',
              dark: '#5d472f'
            },
            background: {
              main: '#000000',
              surface: '#141414',
              surfaceDark: '#1A1A1A'
            },
            font: {
              primary: '#ffffff',
              secondary: '#c0af9c',
              muted: '#d5d8dc',
              disabled: 'rgba(255, 255, 255, 0.5)'
            }
          };
          themeService.saveTheme(defaultTheme);
          console.log('Default theme applied successfully');
        } else {
          console.log('Saved theme found and applied');
        }
      }
    } catch (error) {
      console.error('Error initializing theme:', error);
    }
  }, [config]);

  console.log('App rendering...', {
    formType,
    hasConfig: !!config,
    timestamp: new Date().toISOString()
  });

  // Define placeholders for required props
  const placeholderApiKeys = {
    here: 'YOUR_HERE_API_KEY',
    mapbox: 'YOUR_MAPBOX_API_KEY',
    resend: 'YOUR_RESEND_API_KEY',
    salesmate: 'YOUR_SALESMATE_API_KEY'
  };
  const placeholderNonce = 'your_nonce_here';
  const placeholderAjaxUrl = config?.ajaxUrl || '/wp-admin/admin-ajax.php';
  const placeholderVehicleImages = {
    sedan: 'path/to/sedan.jpg',
    suv: 'path/to/suv.jpg'
    // Add other vehicle types as needed
  };

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <div className="booking-form-container">
        <BookingForm 
          formType={formType} 
          apiKeys={placeholderApiKeys}
          nonce={placeholderNonce}
          ajaxUrl={placeholderAjaxUrl}
          vehicleImages={placeholderVehicleImages}
        />
      </div>
    </ErrorBoundary>
  );
}; 