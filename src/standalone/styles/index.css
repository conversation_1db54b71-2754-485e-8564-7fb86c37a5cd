@import '@core/styles/index.css';

/* Standalone-specific styles */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background);
  color: var(--text-primary);
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.booking-form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

#limo-booking-form {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  color: var(--text-primary);
  background-color: var(--background);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}

/* Reset styles for WordPress compatibility */
#limo-booking-form * {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

#limo-booking-form h1 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: var(--text-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #limo-booking-form {
    padding: 1rem;
  }

  #limo-booking-form h1 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }
}