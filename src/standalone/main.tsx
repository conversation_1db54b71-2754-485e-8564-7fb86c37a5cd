import React from 'react'
import { createRoot } from 'react-dom/client'
import { App } from './App'
import './styles/index.css'
import '@core/styles/index.css'
import '@mantine/core/styles.css'
import { MantineProvider, ColorSchemeScript } from '@mantine/core'
import { mantineTheme } from '@core/theme/mantineTheme'

// Debug logging
console.log('Standalone app initializing...', {
  env: process.env.NODE_ENV,
  isStandalone: process.env.VITE_STANDALONE,
  timestamp: new Date().toISOString()
})

const root = document.getElementById('root')
if (!root) {
  console.error('Root element not found')
  throw new Error('Root element not found')
}

console.log('Root element found, mounting app...')

try {
  createRoot(root).render(
    <React.StrictMode>
      <ColorSchemeScript defaultColorScheme="dark" />
      <MantineProvider theme={mantineTheme} defaultColorScheme="dark">
        <App />
      </MantineProvider>
    </React.StrictMode>
  )
  console.log('App mounted successfully')
} catch (error) {
  console.error('Error mounting app:', error)
  throw error
}
