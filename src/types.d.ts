// Global type definitions
declare global {
  interface Window {
    limoBookingConfig?: {
      /**
       * WordPress AJAX URL
       */
      ajaxUrl?: string;
      /**
       * WordPress REST API base URL
       */
      restUrl?: string;
      /**
       * WordPress Plugin API Proxy endpoint
       * This is the endpoint for API proxy requests
       * Example: /wp-json/limo-booking/v1/proxy/
       */
      apiBaseUrl?: string;
      /**
       * WordPress nonce for security
       */
      nonce?: string;
      /**
       * HERE Maps API key
       */
      hereApiKey?: string;
      /**
       * Mapbox API token
       */
      mapboxToken?: string;
      /**
       * Flag indicating if running in WordPress environment
       */
      isWordPress?: boolean;
      /**
       * Flag to hide theme selector in WordPress environment
       */
      hideThemeSelector?: boolean;
      /**
       * Flag to inherit theme colors from WordPress theme
       */
      inheritThemeColors?: boolean;
      /**
       * Theme configuration
       */
      theme?: {
        primary_color: string;
        primary_light: string;
        primary_dark: string;
        background: string;
        surface: string;
        surface_dark: string;
        text_primary: string;
        text_secondary: string;
        text_muted: string;
        text_disabled: string;
        mapStyle: string;
        mapTheme: "dark" | "light";
      };
      /**
       * Vehicle images mapping
       */
      vehicleImages?: Record<string, string>;
    };
    closeTripSummary?: () => void;
    preventFormScrolling?: boolean;
    isDirectlyApplyingTheme?: boolean;
    scrollFormToTop: () => void;
    wp?: any;
    mapboxgl?: any;
  }
}

export {}; 