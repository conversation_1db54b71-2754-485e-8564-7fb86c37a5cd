:root {
  /* Light Theme */
  --light-background: #ffffff;
  --light-surface: #f3f4f6;
  --light-surface-dark: #e5e7eb;
  --light-border: #d1d5db;
  --light-text-primary: #111827;
  --light-text-secondary: #4b5563;

  /* Dark Theme */
  --dark-background: #000000;
  --dark-surface: #141414;
  --dark-surface-dark: #1a1a1a;
  --dark-border: #333333;
  --dark-text-primary: #ffffff;
  --dark-text-secondary: #9ca3af;

  /* Slate Theme */
  --slate-background: #0f172a;
  --slate-surface: #1e293b;
  --slate-surface-dark: #334155;
  --slate-border: #475569;
  --slate-text-primary: #f8fafc;
  --slate-text-secondary: #cbd5e1;

  /* Navy Theme */
  --navy-background: #0a192f;
  --navy-surface: #112240;
  --navy-surface-dark: #233554;
  --navy-border: #2d3952;
  --navy-text-primary: #e6f1ff;
  --navy-text-secondary: #8892b0;
}

/* Default theme (dark) */
#limo-booking-form {
  --background: var(--dark-background);
  --surface: var(--dark-surface);
  --surface-dark: var(--dark-surface-dark);
  --border: var(--dark-border);
  --text-primary: var(--dark-text-primary);
  --text-secondary: var(--dark-text-secondary);
}

/* Theme classes */
#limo-booking-form.light-theme {
  --background: var(--light-background);
  --surface: var(--light-surface);
  --surface-dark: var(--light-surface-dark);
  --border: var(--light-border);
  --text-primary: var(--light-text-primary);
  --text-secondary: var(--light-text-secondary);
}

#limo-booking-form.slate-theme {
  --background: var(--slate-background);
  --surface: var(--slate-surface);
  --surface-dark: var(--slate-surface-dark);
  --border: var(--slate-border);
  --text-primary: var(--slate-text-primary);
  --text-secondary: var(--slate-text-secondary);
}

#limo-booking-form.navy-theme {
  --background: var(--navy-background);
  --surface: var(--navy-surface);
  --surface-dark: var(--navy-surface-dark);
  --border: var(--navy-border);
  --text-primary: var(--navy-text-primary);
  --text-secondary: var(--navy-text-secondary);
}

/* Ensure text contrast */
#limo-booking-form input,
#limo-booking-form select,
#limo-booking-form textarea {
  color: var(--text-primary);
  background-color: var(--surface);
  border-color: var(--border);
}

#limo-booking-form input::placeholder,
#limo-booking-form textarea::placeholder {
  color: var(--text-secondary);
}

/* Override WordPress theme styles */
#limo-booking-form * {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

#limo-booking-form h1,
#limo-booking-form h2,
#limo-booking-form h3,
#limo-booking-form h4,
#limo-booking-form h5,
#limo-booking-form h6,
#limo-booking-form p,
#limo-booking-form span,
#limo-booking-form label {
  color: var(--text-primary);
}