/**
 * SHADOW DOM CSS ISOLATION STYLES
 * 
 * This file contains all CSS needed for complete isolation within Shadow DOM.
 * These styles are injected directly into the Shadow DOM to prevent any
 * conflicts with WordPress themes or other plugins.
 */

export const getShadowDomStyles = (theme: any) => {
  const primaryColor = theme?.primary_color || '#765a3d';
  const backgroundColor = theme?.background || '#000000';
  const textPrimary = theme?.text_primary || '#ffffff';
  const textSecondary = theme?.text_secondary || '#e5e7eb';
  const surface = theme?.surface || '#141414';
  const surfaceDark = theme?.surface_dark || '#1a1a1a';
  const border = theme?.border || '#333333';

  return `
    /* =============================================================================
       COMPLETE CSS RESET AND ISOLATION
       ============================================================================= */
    
    *, *::before, *::after {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      border: 0;
      font-size: 100%;
      font: inherit;
      vertical-align: baseline;
    }
    
    /* Host element isolation */
    :host {
      all: initial;
      display: block;
      contain: layout style paint;
      isolation: isolate;
    }
    
    /* =============================================================================
       BASE STYLES
       ============================================================================= */
    
    #limo-booking-react-root {
      /* Layout */
      display: block;
      width: 100%;
      min-height: 400px;
      position: relative;
      overflow: hidden;
      border-radius: 12px;
      
      /* Typography */
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      font-size: 16px;
      line-height: 1.5;
      font-weight: 400;
      
      /* Colors */
      color: ${textPrimary};
      background: ${backgroundColor};
      
      /* Isolation */
      isolation: isolate;
      z-index: 1;
    }
    
    /* =============================================================================
       MANTINE COMPONENT STYLES (ESSENTIAL SUBSET)
       ============================================================================= */
    
    /* Mantine CSS Variables */
    #limo-booking-react-root {
      --mantine-color-body: ${backgroundColor};
      --mantine-color-text: ${textPrimary};
      --mantine-color-dimmed: ${textSecondary};
      --mantine-color-primary-filled: ${primaryColor};
      --mantine-color-primary-filled-hover: ${primaryColor}dd;
      --mantine-color-default: ${surface};
      --mantine-color-default-hover: ${surfaceDark};
      --mantine-color-default-border: ${border};
      --mantine-spacing-xs: 0.625rem;
      --mantine-spacing-sm: 0.875rem;
      --mantine-spacing-md: 1rem;
      --mantine-spacing-lg: 1.25rem;
      --mantine-spacing-xl: 1.5rem;
      --mantine-radius-sm: 0.25rem;
      --mantine-radius-md: 0.5rem;
      --mantine-radius-lg: 0.75rem;
      --mantine-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
      --mantine-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    /* Button styles */
    .mantine-Button-root {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      position: relative;
      box-sizing: border-box;
      text-align: center;
      text-decoration: none;
      cursor: pointer;
      user-select: none;
      background: var(--mantine-color-primary-filled);
      color: white;
      border: 1px solid transparent;
      border-radius: var(--mantine-radius-md);
      font-size: 0.875rem;
      font-weight: 600;
      line-height: 1;
      padding: 0.625rem 1rem;
      transition: all 0.15s ease;
    }
    
    .mantine-Button-root:hover {
      background: var(--mantine-color-primary-filled-hover);
    }
    
    /* Input styles */
    .mantine-Input-input {
      appearance: none;
      resize: none;
      box-sizing: border-box;
      font-size: 0.875rem;
      width: 100%;
      color: var(--mantine-color-text);
      display: block;
      text-align: left;
      min-height: 2.25rem;
      padding-left: 0.75rem;
      padding-right: 0.75rem;
      border-radius: var(--mantine-radius-md);
      border: 1px solid var(--mantine-color-default-border);
      background: var(--mantine-color-default);
      transition: border-color 0.15s ease;
    }
    
    .mantine-Input-input:focus {
      outline: none;
      border-color: var(--mantine-color-primary-filled);
    }
    
    /* Select styles */
    .mantine-Select-root {
      position: relative;
    }
    
    .mantine-Select-input {
      cursor: pointer;
    }
    
    /* Paper/Card styles */
    .mantine-Paper-root {
      background: var(--mantine-color-default);
      border-radius: var(--mantine-radius-md);
      padding: var(--mantine-spacing-md);
    }
    
    /* Text styles */
    .mantine-Text-root {
      color: var(--mantine-color-text);
      font-size: 0.875rem;
      line-height: 1.5;
    }
    
    /* Title styles */
    .mantine-Title-root {
      color: var(--mantine-color-text);
      font-weight: 600;
      line-height: 1.2;
    }
    
    /* Group styles */
    .mantine-Group-root {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: var(--mantine-spacing-md);
    }
    
    /* Stack styles */
    .mantine-Stack-root {
      display: flex;
      flex-direction: column;
      gap: var(--mantine-spacing-md);
    }
    
    /* Grid styles */
    .mantine-Grid-root {
      display: flex;
      flex-wrap: wrap;
      margin: calc(var(--mantine-spacing-md) / -2);
    }
    
    .mantine-Grid-col {
      box-sizing: border-box;
      flex-grow: 0;
      padding: calc(var(--mantine-spacing-md) / 2);
    }
    
    /* =============================================================================
       LOADING STATE
       ============================================================================= */
    
    #limo-booking-react-root[data-loading="true"]::after {
      content: "Loading booking form...";
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: ${backgroundColor};
      color: ${textPrimary};
      font-size: 16px;
      z-index: 10;
    }
    
    /* =============================================================================
       RESPONSIVE DESIGN
       ============================================================================= */
    
    @media (max-width: 768px) {
      #limo-booking-react-root {
        border-radius: 0;
        min-height: 300px;
      }
      
      .mantine-Grid-col {
        flex: 0 0 100%;
      }
    }
    
    /* =============================================================================
       UTILITY CLASSES
       ============================================================================= */
    
    .sr-only {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }
    
    /* Force inheritance for all child elements */
    #limo-booking-react-root * {
      font-family: inherit;
      box-sizing: border-box;
    }
    
    /* Prevent any external styles from affecting our components */
    #limo-booking-react-root *:not([class*="mantine"]) {
      all: unset;
      display: revert;
      box-sizing: border-box;
      font-family: inherit;
    }
  `;
};

/**
 * Additional CSS for specific Mantine components that might be used
 */
export const getAdditionalMantineStyles = () => `
  /* DatePicker styles */
  .mantine-DatePicker-root {
    position: relative;
  }
  
  /* Modal styles */
  .mantine-Modal-root {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
  }
  
  /* Notification styles */
  .mantine-Notification-root {
    position: relative;
    border-radius: var(--mantine-radius-md);
    padding: var(--mantine-spacing-md);
    background: var(--mantine-color-default);
    border: 1px solid var(--mantine-color-default-border);
  }
  
  /* Loader styles */
  .mantine-Loader-root {
    display: inline-block;
    position: relative;
  }
`;
