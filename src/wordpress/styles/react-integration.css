/**
 * React Integration Styles for WordPress
 * 
 * This file ensures that the React components maintain their exact styling
 * when integrated with WordPress, preserving the original design and layout.
 */

/* Ensure the booking form container has the correct styling */
.limo-booking-plugin {
  /* Preserve the exact styling from the React version */
  font-family: 'Raleway', sans-serif;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* Ensure WordPress themes don't override our styles */
.limo-booking-plugin * {
  box-sizing: border-box;
}

/* Preserve button styling */
.limo-booking-plugin button {
  font-family: 'Raleway', sans-serif;
  cursor: pointer;
  border: none;
  outline: none;
}

/* Preserve input styling */
.limo-booking-plugin input,
.limo-booking-plugin select,
.limo-booking-plugin textarea {
  font-family: 'Raleway', sans-serif;
  outline: none;
}

/* Ensure proper spacing */
.limo-booking-plugin .limo-bg-surface {
  border-radius: 8px;
}

/* Ensure proper text colors */
.limo-booking-plugin .limo-text {
  color: var(--limo-text-primary);
}

.limo-booking-plugin .limo-text-muted {
  color: var(--limo-text-muted);
}

/* Ensure proper button styling */
.limo-booking-plugin .limo-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
}

.limo-booking-plugin .limo-button-primary {
  background-color: var(--limo-primary);
  color: white;
}

.limo-booking-plugin .limo-button-primary:hover {
  background-color: var(--limo-primary-light);
}

/* Ensure proper input styling */
.limo-booking-plugin .limo-input {
  border: 1px solid var(--limo-border);
  background-color: var(--limo-surface);
  color: var(--limo-text-primary);
  transition: border-color 0.2s ease-in-out;
}

.limo-booking-plugin .limo-input:focus {
  border-color: var(--limo-primary);
}

/* Ensure proper tab styling */
.limo-booking-plugin .limo-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.limo-booking-plugin .limo-tab {
  padding: 8px 16px;
  border-radius: 4px;
  background-color: var(--limo-surface);
  color: var(--limo-text-muted);
  border: 1px solid var(--limo-border);
  cursor: pointer;
}

.limo-booking-plugin .limo-tab[data-active="true"] {
  background-color: var(--limo-primary);
  color: white;
  border-color: var(--limo-primary);
}

/* Ensure proper toggle styling */
.limo-booking-plugin .limo-toggle input:checked + span {
  background-color: var(--limo-primary);
}

.limo-booking-plugin .limo-toggle input:checked + span span {
  transform: translateX(26px);
}

/* Ensure proper responsive layout */
@media (max-width: 768px) {
  .limo-booking-plugin {
    padding: 16px;
  }
  
  .limo-booking-plugin .limo-tabs {
    flex-wrap: wrap;
  }
}