/* WordPress Plugin Specific Styles */

/* Reset container styles */
.limo-booking-form-container {
  width: 100% !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding: 2rem !important;
  font-size: 16px !important;
  line-height: 1.5 !important;
  font-family: 'Raleway', sans-serif !important;
  box-sizing: border-box !important;
}

/* Force proper form sizing and alignment */
.limo-booking-form {
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 1.5rem !important;
}

/* Ensure form elements maintain proper size */
.limo-booking-form input,
.limo-booking-form select,
.limo-booking-form textarea {
  width: 100% !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  padding: 0.75rem 1rem !important;
  border-radius: 0.375rem !important;
  border: 1px solid var(--border) !important;
  background-color: var(--surface) !important;
  color: var(--text-primary) !important;
}

/* Force proper button styling */
.limo-booking-form button {
  font-size: 1rem !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 0.375rem !important;
  font-weight: 600 !important;
  transition: all 0.2s ease-in-out !important;
}

/* Ensure proper step indicator styling */
.booking-steps {
  width: 100% !important;
  display: flex !important;
  justify-content: center !important;
  gap: 2rem !important;
  margin-bottom: 2rem !important;
}

/* Force proper heading styles */
.limo-booking-form h1,
.limo-booking-form h2,
.limo-booking-form h3,
.limo-booking-form h4 {
  font-family: 'Raleway', sans-serif !important;
  color: var(--text-primary) !important;
  margin: 0 0 1rem 0 !important;
  line-height: 1.2 !important;
}

/* Ensure proper spacing between sections */
.booking-section {
  width: 100% !important;
  margin-bottom: 2rem !important;
}

/* Force proper label styling */
.limo-booking-form label {
  display: block !important;
  margin-bottom: 0.5rem !important;
  font-weight: 500 !important;
  color: var(--text-primary) !important;
}

/* Ensure proper grid layout */
.form-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  gap: 1.5rem !important;
  width: 100% !important;
}