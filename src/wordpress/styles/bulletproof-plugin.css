/**
 * BULLETPROOF WORDPRESS PLUGIN CSS
 * 
 * COMMERCIAL-GRADE CSS ISOLATION STRATEGY
 * 
 * This CSS file implements a bulletproof scoping strategy to ensure our
 * React components work perfectly in any WordPress theme without conflicts.
 * 
 * ARCHITECTURE PRINCIPLES:
 * 1. Complete CSS isolation using high-specificity selectors
 * 2. CSS reset within our namespace to prevent theme interference
 * 3. Defensive CSS against common WordPress theme issues
 * 4. No global CSS pollution - everything scoped
 * 5. Performance optimized with minimal CSS footprint
 */

/* =============================================================================
   CSS ISOLATION & RESET FOR PLUGIN CONTAINER
   ============================================================================= */

/* Main plugin container with maximum isolation */
.limo-booking-plugin-container.limo-booking-plugin-container.limo-booking-plugin-container {
  /* CSS Reset - Prevent WordPress theme interference */
  all: initial;
  
  /* Re-establish essential properties */
  display: block;
  box-sizing: border-box;
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  
  /* Typography foundation */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: #1e293b;
  
  /* Ensure our styles take precedence */
  z-index: 1;
  
  /* CSS Variables for theming */
  --limo-primary: #765a3d;
  --limo-primary-light: #8b6d4c;
  --limo-primary-dark: #5d472f;
  --limo-background: #000000;
  --limo-surface: #141414;
  --limo-surface-dark: #1a1a1a;
  --limo-text-primary: #ffffff;
  --limo-text-secondary: #e5e7eb;
  --limo-text-muted: #9ca3af;
  --limo-border: #333333;
  --limo-border-hover: #4d4d4d;
  --limo-success: #10b981;
  --limo-error: #ef4444;
  --limo-warning: #f59e0b;
  --limo-info: #3b82f6;
  
  /* Apply base styling */
  background-color: var(--limo-background);
  color: var(--limo-text-primary);
  border-radius: 12px;
  overflow: hidden;
}

/* Force box-sizing for all child elements */
.limo-booking-plugin-container.limo-booking-plugin-container *,
.limo-booking-plugin-container.limo-booking-plugin-container *::before,
.limo-booking-plugin-container.limo-booking-plugin-container *::after {
  box-sizing: border-box;
}

/* =============================================================================
   DEFENSIVE CSS AGAINST WORDPRESS THEME INTERFERENCE
   ============================================================================= */

/* Reset common WordPress theme overrides */
.limo-booking-plugin-container.limo-booking-plugin-container h1,
.limo-booking-plugin-container.limo-booking-plugin-container h2,
.limo-booking-plugin-container.limo-booking-plugin-container h3,
.limo-booking-plugin-container.limo-booking-plugin-container h4,
.limo-booking-plugin-container.limo-booking-plugin-container h5,
.limo-booking-plugin-container.limo-booking-plugin-container h6 {
  margin: 0;
  padding: 0;
  font-weight: 600;
  line-height: 1.2;
  color: inherit;
  font-family: inherit;
  text-transform: none;
  letter-spacing: normal;
  border: none;
  background: none;
}

/* Reset form elements */
.limo-booking-plugin-container.limo-booking-plugin-container input,
.limo-booking-plugin-container.limo-booking-plugin-container select,
.limo-booking-plugin-container.limo-booking-plugin-container textarea,
.limo-booking-plugin-container.limo-booking-plugin-container button {
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

/* Reset lists */
.limo-booking-plugin-container.limo-booking-plugin-container ul,
.limo-booking-plugin-container.limo-booking-plugin-container ol,
.limo-booking-plugin-container.limo-booking-plugin-container li {
  margin: 0;
  padding: 0;
  list-style: none;
}

/* Reset links */
.limo-booking-plugin-container.limo-booking-plugin-container a {
  color: inherit;
  text-decoration: none;
  background: none;
  border: none;
  outline: none;
}

/* Reset paragraphs and text elements */
.limo-booking-plugin-container.limo-booking-plugin-container p,
.limo-booking-plugin-container.limo-booking-plugin-container span,
.limo-booking-plugin-container.limo-booking-plugin-container div {
  margin: 0;
  padding: 0;
  line-height: inherit;
  color: inherit;
  font-family: inherit;
}

/* =============================================================================
   MANTINE COMPONENT OVERRIDES
   ============================================================================= */

/* Force Mantine components to use our theme variables */
.limo-booking-plugin-container.limo-booking-plugin-container [data-mantine-color-scheme] {
  --mantine-color-body: var(--limo-background);
  --mantine-color-text: var(--limo-text-primary);
  --mantine-color-primary-filled: var(--limo-primary);
  --mantine-color-primary-filled-hover: var(--limo-primary-light);
  --mantine-color-default-border: var(--limo-border);
  --mantine-color-default: var(--limo-surface);
  --mantine-color-default-hover: var(--limo-surface-dark);
}

/* =============================================================================
   RESPONSIVE DESIGN
   ============================================================================= */

/* Mobile responsiveness */
@media screen and (max-width: 768px) {
  .limo-booking-plugin-container.limo-booking-plugin-container {
    margin: 0;
    border-radius: 0;
    max-width: 100%;
  }
}

/* =============================================================================
   LOADING STATE
   ============================================================================= */

/* Loading indicator */
.limo-booking-plugin-container.limo-booking-plugin-container[data-loading="true"]::after {
  content: "Loading booking form...";
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--limo-text-secondary);
  font-size: 16px;
}

/* =============================================================================
   THEME VARIANTS
   ============================================================================= */

/* Light theme variant */
.limo-booking-plugin-container.limo-booking-plugin-container[data-theme="light"] {
  --limo-background: #ffffff;
  --limo-surface: #f8f9fa;
  --limo-surface-dark: #e9ecef;
  --limo-text-primary: #1e293b;
  --limo-text-secondary: #475569;
  --limo-text-muted: #64748b;
  --limo-border: #e2e8f0;
  --limo-border-hover: #cbd5e1;
}

/* Dark theme variant (default) */
.limo-booking-plugin-container.limo-booking-plugin-container[data-theme="dark"] {
  --limo-background: #000000;
  --limo-surface: #141414;
  --limo-surface-dark: #1a1a1a;
  --limo-text-primary: #ffffff;
  --limo-text-secondary: #e5e7eb;
  --limo-text-muted: #9ca3af;
  --limo-border: #333333;
  --limo-border-hover: #4d4d4d;
}

/* =============================================================================
   INTEGRATION NOTES
   ============================================================================= */

/*
USAGE INSTRUCTIONS:

1. Wrap your React component in a container with these classes:
   <div className="limo-booking-plugin-container" data-theme="dark" data-loading="false">
     {/* Your React components here */}
   </div>

2. This CSS file should be loaded AFTER any WordPress theme CSS to ensure proper precedence.

3. The high specificity selectors (.limo-booking-plugin-container.limo-booking-plugin-container)
   ensure our styles take precedence without using !important.

4. All styles are scoped to prevent conflicts with WordPress themes.

5. CSS variables allow for easy theming and customization.
*/
