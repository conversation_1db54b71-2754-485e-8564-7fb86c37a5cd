// @ts-nocheck
/// <reference path="../core/components/BookingForm/index.tsx" />
import React from 'react';
import ReactDOM from 'react-dom/client';
import * as BookingFormModule from '@core/components/BookingForm';
import { ErrorBoundary } from 'react-error-boundary';
import { MantineProvider, ColorSchemeScript } from '@mantine/core';
import { wordPressMantineTheme } from '@core/theme/wordPressMantineTheme';

// Import standalone CSS directly for complete styling
import '@core/styles/standalone-index.css';

// Extract BookingForm component
const { BookingForm } = BookingFormModule;

// MINIMAL STANDALONE APPROACH: Use standalone CSS with minimal WordPress interaction
const createMinimalContainer = (container: HTMLElement, settings: any, config: any) => {
  console.log('MINIMAL: Creating minimal standalone-like container');
  
  // Clear the container
  container.innerHTML = '';
  
  // Create a simple container with standalone-like styling
  const minimalWrapper = document.createElement('div');
  minimalWrapper.className = 'limo-minimal-standalone-wrapper';
  minimalWrapper.style.cssText = `
    width: 100% !important;
    min-height: 600px !important;
    position: relative !important;
    isolation: isolate !important;
    z-index: 999 !important;
    background: ${settings.theme?.background || '#000000'} !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  `;
  
  // Create React container
  const reactContainer = document.createElement('div');
  reactContainer.id = 'limo-minimal-react-root';
  reactContainer.className = 'standalone-app'; // Use standalone class
  reactContainer.style.cssText = `
    width: 100% !important;
    height: 100% !important;
    min-height: 600px !important;
    position: relative !important;
  `;
  
  minimalWrapper.appendChild(reactContainer);
  container.appendChild(minimalWrapper);
  
  // Inject minimal CSS that just ensures our standalone styles work
  const minimalStyleId = `limo-minimal-${Date.now()}`;
  const minimalStyle = document.createElement('style');
  minimalStyle.id = minimalStyleId;
  minimalStyle.textContent = `
    /* MINIMAL STANDALONE CSS - PRESERVE EXISTING STYLES */
    .limo-minimal-standalone-wrapper {
      /* Just ensure our container is isolated */
      contain: layout style paint !important;
    }
    
    .limo-minimal-standalone-wrapper * {
      /* Don't reset anything - let standalone CSS work */
      box-sizing: border-box !important;
    }
    
    /* Ensure standalone styles take precedence */
    .limo-minimal-standalone-wrapper .standalone-app {
      /* Let standalone CSS handle everything */
    }
  `;
  
  document.head.appendChild(minimalStyle);
  console.log('MINIMAL: Minimal CSS injected, relying on standalone styles');
  
  return reactContainer;
};

// Minimal initialization function
(window as any).limoBookingMinimalInit = (containerId?: string, formType?: string) => {
  console.log('MINIMAL: Starting minimal standalone-like initialization');
  
  const container = containerId ? document.getElementById(containerId) : document.querySelector('.limo-booking-plugin');
  if (!container) {
    console.error('MINIMAL: Container not found');
    return false;
  }
  
  const config = (window as any).limoBookingConfig || {};
  const settings = {
    theme: config.theme || {},
    apiKeys: {
      mapbox: config.mapboxToken || '',
      here: config.hereApiKey || '',
      resend: config.resendApiKey || '',
      salesmate: config.salesmateApiKey || ''
    },
    vehicleImages: config.vehicleImages || {},
    hideThemeSelector: config.hideThemeSelector || false,
    inheritThemeColors: config.inheritThemeColors || false
  };
  
  console.log('MINIMAL: Settings:', settings);
  
  try {
    // Create minimal container
    const reactContainer = createMinimalContainer(container, settings, config);
    
    // Determine color scheme
    const backgroundColor = settings.theme?.background || '#000000';
    const isLightBackground = (color: string): boolean => {
      const hex = color.replace('#', '');
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);
      const brightness = (r * 299 + g * 587 + b * 114) / 1000;
      return brightness > 128;
    };
    const colorScheme = isLightBackground(backgroundColor) ? 'light' : 'dark';
    
    // Create React root and render
    const root = ReactDOM.createRoot(reactContainer);
    root.render(
      <React.StrictMode>
        <ColorSchemeScript defaultColorScheme={colorScheme} />
        <MantineProvider theme={wordPressMantineTheme} defaultColorScheme={colorScheme}>
          <ErrorBoundary
            FallbackComponent={({ error }) => (
              <div style={{
                padding: '20px',
                border: '2px solid #ef4444',
                borderRadius: '8px',
                background: '#fef2f2',
                color: '#991b1b',
                textAlign: 'center'
              }}>
                <h3>Minimal Standalone Error</h3>
                <p>{error.message}</p>
                <button onClick={() => window.location.reload()}>Reload</button>
              </div>
            )}
          >
            <BookingForm
              formType={formType as any || 'point-to-point'}
              apiKeys={settings.apiKeys}
              nonce={config.nonce || ''}
              ajaxUrl={config.ajaxUrl || '/wp-admin/admin-ajax.php'}
              vehicleImages={settings.vehicleImages}
              hideThemeSelector={settings.hideThemeSelector}
              inheritThemeColors={settings.inheritThemeColors}
            />
          </ErrorBoundary>
        </MantineProvider>
      </React.StrictMode>
    );
    
    console.log('MINIMAL: React app rendered successfully with standalone CSS');
    return true;
  } catch (error) {
    console.error('MINIMAL: Error during initialization:', error);
    return false;
  }
};

// Auto-initialize on DOM ready
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(() => (window as any).limoBookingMinimalInit(), 100);
} else {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => (window as any).limoBookingMinimalInit(), 100);
  });
}
