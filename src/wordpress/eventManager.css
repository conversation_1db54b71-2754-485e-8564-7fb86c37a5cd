/**
 * Event Manager CSS
 * 
 * Styles for handling hash navigation and preventing scroll interference
 * in WordPress environments.
 */

/* When hash navigation is active, ensure our form doesn't interfere */
.limo-booking-hash-navigation #limo-booking-form {
  position: relative;
  z-index: 1;
}

/* Prevent scroll animations when hash navigation is active */
.limo-booking-hash-navigation {
  scroll-behavior: auto !important;
}

/* Ensure form is visible but doesn't interfere with hash navigation */
.limo-booking-hash-navigation .limo-booking-form-container {
  opacity: 1;
  transition: none;
}

/* Prevent any scroll animations from our components when hash navigation is active */
.limo-booking-hash-navigation * {
  scroll-margin-top: 0 !important;
  scroll-padding-top: 0 !important;
}