/**
 * Event Manager for WordPress Integration
 * 
 * This module provides a robust solution for handling DOM events in WordPress environments
 * without relying on jQuery patching. It uses scoped event handling, defensive coding practices,
 * and proper namespacing to avoid conflicts with WordPress themes and plugins.
 */

// Define a namespace for our event handling to avoid global conflicts
const NAMESPACE = 'limo-booking';

// Interface for scroll prevention options
interface ScrollPreventionOptions {
  preventOnHash: boolean;
  preventOnFormVisible: boolean;
  debugMode: boolean;
}

// Default options
const defaultOptions: ScrollPreventionOptions = {
  preventOnHash: true,
  preventOnFormVisible: true,
  debugMode: false
};

/**
 * Event Manager class that handles DOM events in a safe, scoped manner
 */
export class EventManager {
  private options: ScrollPreventionOptions;
  private isScrollingPrevented: boolean = false;
  private originalScrollBehavior: string | null = null;
  private formElement: HTMLElement | null = null;
  
  constructor(options: Partial<ScrollPreventionOptions> = {}) {
    this.options = { ...defaultOptions, ...options };
    this.log('EventManager initialized with options:', this.options);
  }
  
  /**
   * Initialize the event manager and apply necessary protections
   */
  public initialize(): void {
    this.log('Initializing EventManager');
    this.formElement = document.getElementById('limo-booking-form');
    
    // Check for hash in URL
    if (this.options.preventOnHash && window.location.hash) {
      this.preventScrolling();
      this.log('Hash detected in URL, scroll prevention activated');
    }
    
    // Add event listeners
    this.setupEventListeners();
  }
  
  /**
   * Set up all necessary event listeners
   */
  private setupEventListeners(): void {
    // Listen for hash changes
    window.addEventListener('hashchange', this.handleHashChange);
    
    // Listen for anchor clicks
    document.addEventListener('click', this.handleAnchorClick, true);
    
    // Listen for scroll events to prevent unwanted scrolling
    if (this.options.preventOnFormVisible && this.formElement) {
      window.addEventListener('scroll', this.handleScroll, { passive: false });
    }
  }
  
  /**
   * Handle hash changes in the URL
   */
  private handleHashChange = (): void => {
    if (this.options.preventOnHash && window.location.hash) {
      this.preventScrolling();
      this.log('Hash changed, scroll prevention activated');
    } else {
      this.allowScrolling();
    }
  }
  
  /**
   * Handle clicks on anchor elements
   */
  private handleAnchorClick = (event: MouseEvent): void => {
    const target = event.target as HTMLElement;
    const anchor = target.closest('a');
    
    if (anchor && anchor.getAttribute('href')?.startsWith('#')) {
      this.log('Anchor with hash clicked:', anchor.getAttribute('href'));
      
      // If the anchor is targeting our form, handle it specially
      if (this.formElement && anchor.getAttribute('href') === '#limo-booking-form') {
        // We'll handle the scrolling ourselves
        event.preventDefault();
        
        this.formElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
        
        // Update URL without scrolling
        window.history.pushState(null, '', anchor.getAttribute('href'));
      } else {
        // For other hash links, prevent our form from interfering
        this.preventScrolling();
      }
    }
  }
  
  /**
   * Handle scroll events
   */
  private handleScroll = (event: Event): void => {
    if (this.isScrollingPrevented && this.options.preventOnHash && window.location.hash) {
      // Only prevent default if we're sure this is an unwanted scroll
      if (event.target === document || event.target === window || 
          event.target === document.documentElement || event.target === document.body) {
        event.preventDefault();
        this.log('Prevented unwanted scroll event');
      }
    }
  }
  
  /**
   * Prevent scrolling behavior
   */
  public preventScrolling(): void {
    if (this.isScrollingPrevented) return;
    
    this.isScrollingPrevented = true;
    document.documentElement.classList.add(`${NAMESPACE}-hash-navigation`);
    
    // Save original scroll behavior
    this.originalScrollBehavior = document.documentElement.style.scrollBehavior;
    document.documentElement.style.scrollBehavior = 'auto';
    
    this.log('Scroll prevention activated');
  }
  
  /**
   * Allow scrolling behavior
   */
  public allowScrolling(): void {
    if (!this.isScrollingPrevented) return;
    
    this.isScrollingPrevented = false;
    document.documentElement.classList.remove(`${NAMESPACE}-hash-navigation`);
    
    // Restore original scroll behavior
    if (this.originalScrollBehavior !== null) {
      document.documentElement.style.scrollBehavior = this.originalScrollBehavior;
    } else {
      document.documentElement.style.removeProperty('scroll-behavior');
    }
    
    this.log('Scroll prevention deactivated');
  }
  
  /**
   * Safely scroll the form into view
   */
  public scrollFormToTop(): void {
    if (this.isScrollingPrevented || !this.formElement) return;
    
    try {
      this.formElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
      this.log('Scrolled form into view');
    } catch (error) {
      this.log('Error scrolling form into view:', error);
    }
  }
  
  /**
   * Clean up event listeners
   */
  public cleanup(): void {
    window.removeEventListener('hashchange', this.handleHashChange);
    document.removeEventListener('click', this.handleAnchorClick, true);
    window.removeEventListener('scroll', this.handleScroll);
    this.allowScrolling();
    this.log('EventManager cleaned up');
  }
  
  /**
   * Utility method for conditional logging
   */
  private log(message: string, ...data: any[]): void {
    if (this.options.debugMode) {
      console.log(`[${NAMESPACE}] ${message}`, ...data);
    }
  }
}

// Create and export a singleton instance
const eventManager = new EventManager({
  debugMode: process.env.NODE_ENV !== 'production'
});

export default eventManager;