/**
 * WordPress Integration Initialization Script
 * 
 * This script initializes the React booking form in WordPress environments
 * while maintaining the exact same design, layout, and behavior as the standalone version.
 */

import { createRoot } from 'react-dom/client';
import { BookingForm } from '@core/components/BookingForm';
import { BookingType } from '@core/store/bookingStore';
import { themeService } from '@core/services/themeService';
import '@core/styles/index.css';
import './styles/react-integration.css';
import { ErrorBoundary } from 'react-error-boundary';
import React from 'react';

// Define the global window interface
declare global {
  interface Window {
    limoBookingInit: (containerId: string, formType?: BookingType) => boolean;
    limoBookingStatus?: {
      initialized: boolean;
      error?: string;
    };
  }
}

// Define the initialization function that will be called by WordPress
window.limoBookingInit = function(containerId: string, formType: BookingType = 'point-to-point'): boolean {
  // Get the container element
  const container = document.getElementById(containerId);
  if (!container) {
    console.error(`Limo Booking: Container element with ID "${containerId}" not found.`);
    return false;
  }

  // Log initialization
  console.log('Limo Booking: Initializing React app in WordPress', {
    containerId,
    formType,
    config: window.limoBookingConfig,
    timestamp: new Date().toISOString()
  });

  // Apply theme from WordPress settings if available
  // if (window.limoBookingConfig?.theme) { // This check is implicitly handled by themeService.loadTheme()
  //   // WordPress theme is automatically loaded by themeService
  //   console.log('Limo Booking: WordPress theme configuration detected');
  // }

  // Load and apply theme
  // themeService.loadTheme() should prioritize limoBookingConfig.theme
  const initialTheme = themeService.loadTheme(); 
  if (initialTheme) {
      themeService.applyTheme(initialTheme);
      console.log('Limo Booking: Theme loaded and applied via themeService.');
  } else {
      console.warn('Limo Booking: No theme could be loaded by themeService.');
  }
  // The data-theme attribute logic is removed as themeService.loadTheme() is now the primary source of truth
  // for initial theme loading. If data-theme needs to be a fallback, themeService.loadTheme() should be updated.

  // Create React root and render the BookingForm component
  try {
    const root = createRoot(container);
    const errorFallback = React.createElement('div', { className: 'limo-error' }, 'Something went wrong loading the booking form. Please refresh the page.');
    
    // Get theme-related flags from config, providing defaults
    const hideThemeSelector = window.limoBookingConfig?.hideThemeSelector ?? false;
    const inheritThemeColors = window.limoBookingConfig?.inheritThemeColors ?? false;

    // Prepare API keys from config
    const apiKeys = {
      here: window.limoBookingConfig?.hereApiKey || '',
      mapbox: window.limoBookingConfig?.mapboxToken || '',
      resend: '',  // These might not be available in the config
      salesmate: '',
    };

    // Get other required props from config
    const nonce = window.limoBookingConfig?.nonce || '';
    const ajaxUrl = window.limoBookingConfig?.ajaxUrl || '';
    const vehicleImages = window.limoBookingConfig?.vehicleImages || {};

    const bookingFormProps = {
      formType: formType as BookingType,
      hideThemeSelector: hideThemeSelector,
      inheritThemeColors: inheritThemeColors,
      apiKeys,
      nonce,
      ajaxUrl,
      vehicleImages
    };

    const errorBoundary = React.createElement(ErrorBoundary, { fallback: errorFallback }, 
      React.createElement(BookingForm, bookingFormProps)
    );
    
    root.render(errorBoundary);
    
    // Set initialization status
    window.limoBookingStatus = { initialized: true };
    
    // Dispatch custom event for WordPress integration
    const event = new CustomEvent('limoBookingInitialized', { 
      bubbles: true,
      detail: { containerId, formType }
    });
    document.dispatchEvent(event);
    
    return true;
  } catch (error) {
    console.error('Limo Booking: Failed to initialize React app', error);
    window.limoBookingStatus = { 
      initialized: false,
      error: error instanceof Error ? error.message : String(error)
    };
    return false;
  }
};

// Auto-initialize if there's a container with the default ID
document.addEventListener('DOMContentLoaded', () => {
  const defaultContainer = document.getElementById('limo-booking-form');
  if (defaultContainer) {
    const formType = defaultContainer.getAttribute('data-form-type') as BookingType || 'point-to-point';
    window.limoBookingInit('limo-booking-form', formType);
  }
});