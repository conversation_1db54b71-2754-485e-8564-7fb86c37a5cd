// @ts-nocheck
/// <reference path="../core/components/BookingForm/index.tsx" />
import React from 'react';
import ReactDOM from 'react-dom/client';
import * as BookingFormModule from '@core/components/BookingForm';
import { ErrorBoundary } from 'react-error-boundary';

// Import standalone CSS for complete styling without Mantine conflicts
import '@core/styles/standalone-index.css';
import 'react-datepicker/dist/react-datepicker.css';

// Extract BookingForm component
const { BookingForm } = BookingFormModule;

// NUCLEAR ISOLATION: Complete WordPress CSS isolation
const createNuclearIsolation = (container: HTMLElement, settings: any, config: any) => {
  console.log('NUCLEAR: Creating complete WordPress isolation');

  // Clear the container
  container.innerHTML = '';

  // Create isolated wrapper that acts like an iframe
  const isolatedWrapper = document.createElement('div');
  isolatedWrapper.style.cssText = `
    all: initial !important;
    display: block !important;
    width: 100% !important;
    min-height: 600px !important;
    position: relative !important;
    isolation: isolate !important;
    z-index: 999999 !important;
    contain: layout style paint !important;
    background: ${settings.theme?.background || '#000000'} !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  `;

  // Create React container inside the isolated wrapper
  const reactContainer = document.createElement('div');
  reactContainer.id = 'limo-nuclear-react-root';
  reactContainer.style.cssText = `
    all: initial !important;
    display: block !important;
    width: 100% !important;
    height: 100% !important;
    min-height: 600px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-size: 16px !important;
    line-height: 1.5 !important;
    color: ${settings.theme?.text_primary || '#ffffff'} !important;
    background: ${settings.theme?.background || '#000000'} !important;
    box-sizing: border-box !important;
    position: relative !important;
  `;

  isolatedWrapper.appendChild(reactContainer);
  container.appendChild(isolatedWrapper);

  // Inject NUCLEAR CSS that completely resets everything
  const nuclearStyleId = `limo-nuclear-${Date.now()}`;
  const nuclearStyle = document.createElement('style');
  nuclearStyle.id = nuclearStyleId;
  nuclearStyle.textContent = `
    /* NUCLEAR CSS ISOLATION - PRESERVE STANDALONE STYLING */
    #limo-nuclear-react-root {
      /* Isolate from WordPress but preserve all internal styling */
      contain: layout style paint !important;
      isolation: isolate !important;
      position: relative !important;
      z-index: 999999 !important;
      box-sizing: border-box !important;

      /* Let standalone CSS handle everything inside */
      font-family: inherit !important;
      font-size: inherit !important;
      line-height: inherit !important;
      color: inherit !important;
    }

    /* Minimal WordPress interference - only fix box-sizing */
    #limo-nuclear-react-root * {
      box-sizing: border-box !important;
    }

    /* Restore essential properties */
    #limo-nuclear-react-root {
      display: block !important;
      width: 100% !important;
      height: 100% !important;
      min-height: 600px !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      font-size: 16px !important;
      line-height: 1.5 !important;
      color: ${settings.theme?.text_primary || '#ffffff'} !important;
      background: ${settings.theme?.background || '#000000'} !important;
      position: relative !important;

      /* Mantine CSS Variables */
      --mantine-color-primary: ${settings.theme?.primary_color || '#765a3d'} !important;
      --mantine-color-dark-7: ${settings.theme?.background || '#000000'} !important;
      --mantine-color-dark-6: ${settings.theme?.surface || '#141414'} !important;
      --mantine-color-dark-4: #373A40 !important;
      --mantine-color-white: ${settings.theme?.text_primary || '#ffffff'} !important;
      --mantine-radius-md: 8px !important;
      --mantine-spacing-md: 16px !important;
      --mantine-spacing-lg: 24px !important;
      --mantine-spacing-xl: 32px !important;
    }

    /* Let standalone CSS handle all styling - minimal interference */
  `;

  document.head.appendChild(nuclearStyle);
  console.log('NUCLEAR: Complete CSS isolation injected');

  return reactContainer;
};

// Nuclear initialization function
(window as any).limoBookingNuclearInit = (containerId?: string, formType?: string) => {
  console.log('NUCLEAR: Starting nuclear isolation initialization');

  const container = containerId ? document.getElementById(containerId) : document.querySelector('.limo-booking-plugin');
  if (!container) {
    console.error('NUCLEAR: Container not found');
    return false;
  }

  const config = (window as any).limoBookingConfig || {};
  const settings = {
    theme: config.theme || {},
    apiKeys: {
      mapbox: config.mapboxToken || '',
      here: config.hereApiKey || '',
      resend: config.resendApiKey || '',
      salesmate: config.salesmateApiKey || ''
    },
    vehicleImages: config.vehicleImages || {},
    hideThemeSelector: config.hideThemeSelector || false,
    inheritThemeColors: config.inheritThemeColors || false
  };

  console.log('NUCLEAR: Settings:', settings);

  try {
    // Create nuclear isolation
    const reactContainer = createNuclearIsolation(container, settings, config);

    // Determine color scheme
    const backgroundColor = settings.theme?.background || '#000000';
    const isLightBackground = (color: string): boolean => {
      const hex = color.replace('#', '');
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);
      const brightness = (r * 299 + g * 587 + b * 114) / 1000;
      return brightness > 128;
    };
    const colorScheme = isLightBackground(backgroundColor) ? 'light' : 'dark';

    // Create React root and render
    const root = ReactDOM.createRoot(reactContainer);
    root.render(
      <React.StrictMode>
        <ErrorBoundary
          FallbackComponent={({ error }) => (
            <div style={{
              padding: '20px',
              border: '2px solid #ef4444',
              borderRadius: '8px',
              background: '#fef2f2',
              color: '#991b1b',
              textAlign: 'center'
            }}>
              <h3>Nuclear Isolation Error</h3>
              <p>{error.message}</p>
              <button onClick={() => window.location.reload()}>Reload</button>
            </div>
          )}
        >
          <div className="standalone-app">
            <BookingForm
              formType={formType as any || 'point-to-point'}
              apiKeys={settings.apiKeys}
              nonce={config.nonce || ''}
              ajaxUrl={config.ajaxUrl || '/wp-admin/admin-ajax.php'}
              vehicleImages={settings.vehicleImages}
              hideThemeSelector={settings.hideThemeSelector}
              inheritThemeColors={settings.inheritThemeColors}
            />
          </div>
        </ErrorBoundary>
      </React.StrictMode>
    );

    console.log('NUCLEAR: React app rendered successfully');
    return true;
  } catch (error) {
    console.error('NUCLEAR: Error during initialization:', error);
    return false;
  }
};

// Auto-initialize on DOM ready
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(() => (window as any).limoBookingNuclearInit(), 100);
} else {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => (window as any).limoBookingNuclearInit(), 100);
  });
}
