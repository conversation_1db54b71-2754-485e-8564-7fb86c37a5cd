// @ts-nocheck
/// <reference path="../core/components/BookingForm/index.tsx" />
import React from 'react';
import ReactDOM from 'react-dom/client';
import { ErrorBoundary } from 'react-error-boundary';

// Import standalone CSS for complete styling without Mantine conflicts
import '@core/styles/standalone-index.css';
import 'react-datepicker/dist/react-datepicker.css';

// Import only the essential components that work without Mantine
import { DateTimePicker } from '@core/components/BookingForm/Forms/shared/DateTimePicker';
import { ToggleButton } from '@core/components/BookingForm/Forms/shared/ToggleButton';
import { LocationAutocomplete } from '@core/components/BookingForm/Forms/shared/LocationAutocomplete';
import { useBookingStore } from '@core/store/bookingStore';

// Simple form component without complex Mantine dependencies
const SimpleBookingForm = ({ formType, apiKeys, nonce, ajaxUrl, vehicleImages, hideThemeSelector, inheritThemeColors }) => {
  const { currentStep, setStep } = useBookingStore();

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="flex space-x-4 mb-6">
              <button className="px-4 py-2 bg-primary text-white rounded-lg">Point to Point</button>
              <button className="px-4 py-2 bg-surface text-theme-primary rounded-lg">Hourly</button>
              <button className="px-4 py-2 bg-surface text-theme-primary rounded-lg">Airport</button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <h3 className="text-theme-primary text-base mb-2">Pickup Location:</h3>
                <input
                  type="text"
                  className="w-full h-[46px] bg-surface text-theme-primary border border-border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50"
                  placeholder="Enter pickup location"
                />
              </div>
              <div>
                <h3 className="text-theme-primary text-base mb-2">Dropoff Location:</h3>
                <input
                  type="text"
                  className="w-full h-[46px] bg-surface text-theme-primary border border-border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50"
                  placeholder="Enter dropoff location"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <h3 className="text-theme-primary text-base mb-2">Pickup Date & Time:</h3>
                <DateTimePicker
                  date={new Date()}
                  onChange={(date) => console.log('Date changed:', date)}
                />
              </div>
              <div className="flex items-end">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-theme-muted">Return Trip</span>
                  <ToggleButton
                    checked={false}
                    onChange={(checked) => console.log('Toggle changed:', checked)}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                onClick={() => setStep(2)}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
              >
                Next: Select Vehicle →
              </button>
            </div>
          </div>
        );
      case 2:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-light text-primary">Select Vehicle</h2>
            <p className="text-theme-muted">Vehicle selection coming soon...</p>
            <div className="flex justify-between">
              <button 
                onClick={() => setStep(1)}
                className="px-4 py-2 bg-transparent border border-border text-theme-primary rounded-lg hover:bg-surface-light transition-colors"
              >
                ← Back
              </button>
              <button 
                onClick={() => setStep(3)}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
              >
                Next: Request Quote →
              </button>
            </div>
          </div>
        );
      case 3:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-light text-primary">Request Quote</h2>
            <div className="bg-surface p-6 rounded-lg border border-border">
              <form className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-theme-primary mb-1">Full Name</label>
                    <input 
                      type="text" 
                      className="w-full h-[46px] bg-surface text-theme-primary border border-border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50"
                      placeholder="Enter your full name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-theme-primary mb-1">Email</label>
                    <input 
                      type="email" 
                      className="w-full h-[46px] bg-surface text-theme-primary border border-border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50"
                      placeholder="Enter your email"
                      required
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-theme-primary mb-1">Phone Number</label>
                  <input 
                    type="tel" 
                    className="w-full h-[46px] bg-surface text-theme-primary border border-border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50"
                    placeholder="Enter your phone number"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-theme-primary mb-1">Special Requests (Optional)</label>
                  <textarea 
                    className="w-full min-h-[100px] bg-surface text-theme-primary border border-border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 resize-vertical"
                    placeholder="Any special requests or notes for your trip..."
                    rows={3}
                  />
                </div>
                <div className="flex justify-between pt-4">
                  <button 
                    type="button"
                    onClick={() => setStep(2)}
                    className="px-4 py-2 bg-transparent border border-border text-theme-primary rounded-lg hover:bg-surface-light transition-colors"
                  >
                    ← Back
                  </button>
                  <button 
                    type="submit"
                    className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
                  >
                    Submit Request →
                  </button>
                </div>
              </form>
            </div>
          </div>
        );
      default:
        return <div>Step not found</div>;
    }
  };

  return (
    <div className="relative min-h-[600px] w-full max-w-7xl mx-auto bg-background text-text-primary rounded-lg shadow-lg overflow-hidden">
      <div className="flex flex-col lg:flex-row h-full">
        {/* Trip Summary Sidebar */}
        <div className="lg:w-1/3 bg-surface border-r border-border p-6">
          <h3 className="text-lg font-medium text-primary mb-4">Trip Summary</h3>
          <div className="space-y-3 text-sm text-theme-secondary">
            <div>
              <span className="text-theme-muted">From:</span>
              <div className="text-theme-primary">Select pickup location</div>
            </div>
            <div>
              <span className="text-theme-muted">To:</span>
              <div className="text-theme-primary">Select dropoff location</div>
            </div>
            <div>
              <span className="text-theme-muted">Date & Time:</span>
              <div className="text-theme-primary">Select date and time</div>
            </div>
            <div className="border-t border-border pt-3 mt-4">
              <span className="text-theme-muted">Estimated Total:</span>
              <div className="text-lg font-medium text-primary">Quote on request</div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          <div className="mb-6">
            <div className="flex items-center space-x-4">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${currentStep >= 1 ? 'bg-primary text-white' : 'bg-surface text-theme-muted'}`}>
                1
              </div>
              <div className={`flex-1 h-1 ${currentStep > 1 ? 'bg-primary' : 'bg-surface'}`}></div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${currentStep >= 2 ? 'bg-primary text-white' : 'bg-surface text-theme-muted'}`}>
                2
              </div>
              <div className={`flex-1 h-1 ${currentStep > 2 ? 'bg-primary' : 'bg-surface'}`}></div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${currentStep >= 3 ? 'bg-primary text-white' : 'bg-surface text-theme-muted'}`}>
                3
              </div>
            </div>
            <div className="flex justify-between mt-2 text-sm text-theme-muted">
              <span>Trip Details</span>
              <span>Select Vehicle</span>
              <span>Request Quote</span>
            </div>
          </div>

          <div className="space-y-6">
            {renderCurrentStep()}
          </div>
        </div>
      </div>
    </div>
  );
};

const ErrorFallback = ({ error }: { error: Error }) => (
  <div style={{
    padding: '20px',
    border: '2px solid #ef4444',
    borderRadius: '8px',
    background: '#fef2f2',
    color: '#991b1b',
    textAlign: 'center'
  }}>
    <h3>Nuclear Isolation Error</h3>
    <p>{error.message}</p>
    <button onClick={() => window.location.reload()}>Reload</button>
  </div>
);

// Nuclear initialization function
(window as any).limoBookingNuclearInit = (containerId?: string, formType?: string) => {
  console.log('NUCLEAR: Starting minimal nuclear isolation initialization');

  const container = containerId ? document.getElementById(containerId) : document.querySelector('.limo-booking-plugin');
  if (!container) {
    console.error('NUCLEAR: Container not found');
    return false;
  }

  const config = (window as any).limoBookingConfig || {};
  const settings = {
    theme: config.theme || {},
    apiKeys: {
      mapbox: config.mapboxToken || '',
      here: config.hereApiKey || '',
      resend: config.resendApiKey || '',
      salesmate: config.salesmateApiKey || ''
    },
    vehicleImages: config.vehicleImages || {},
    hideThemeSelector: config.hideThemeSelector || false,
    inheritThemeColors: config.inheritThemeColors || false
  };

  console.log('NUCLEAR: Settings:', settings);

  try {
    // Create isolated wrapper
    const isolatedWrapper = document.createElement('div');
    isolatedWrapper.style.cssText = `
      all: initial !important;
      display: block !important;
      width: 100% !important;
      min-height: 600px !important;
      position: relative !important;
      isolation: isolate !important;
      z-index: 999999 !important;
      contain: layout style paint !important;
      background: ${settings.theme?.background || '#000000'} !important;
      border-radius: 12px !important;
      overflow: hidden !important;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    `;

    // Create React container
    const reactContainer = document.createElement('div');
    reactContainer.id = 'limo-nuclear-react-root';
    reactContainer.style.cssText = `
      width: 100% !important;
      height: 100% !important;
      min-height: 600px !important;
    `;

    isolatedWrapper.appendChild(reactContainer);
    container.appendChild(isolatedWrapper);

    // Create React root and render
    const root = ReactDOM.createRoot(reactContainer);
    root.render(
      <React.StrictMode>
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <div className="standalone-app">
            <SimpleBookingForm
              formType={formType as any || 'point-to-point'}
              apiKeys={settings.apiKeys}
              nonce={config.nonce || ''}
              ajaxUrl={config.ajaxUrl || '/wp-admin/admin-ajax.php'}
              vehicleImages={settings.vehicleImages}
              hideThemeSelector={settings.hideThemeSelector}
              inheritThemeColors={settings.inheritThemeColors}
            />
          </div>
        </ErrorBoundary>
      </React.StrictMode>
    );

    console.log('NUCLEAR: Minimal React app rendered successfully');
    return true;
  } catch (error) {
    console.error('NUCLEAR: Error during initialization:', error);
    return false;
  }
};

// Auto-initialize on DOM ready
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(() => (window as any).limoBookingNuclearInit(), 100);
} else {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => (window as any).limoBookingNuclearInit(), 100);
  });
}
