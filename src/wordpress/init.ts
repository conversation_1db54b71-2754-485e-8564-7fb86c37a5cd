import React from 'react';
import { createRoot } from 'react-dom/client';
import { BookingForm } from '@core/components/BookingForm';
import type { BookingFormProps } from '@core/components/BookingForm/types';
import { type BookingType } from '@core/types/booking';

/**
 * Initialization script for the Limo Booking Form
 */

// Interface for initialization status
interface InitStatus {
  initialized: boolean;
  error?: string;
}

// window.limoBookingConfig is globally defined (see src/types.d.ts).
// This script may interact with a legacy version of it, so we cast to 'any' when using it.

declare global {
  interface Window {
    React: typeof React;
    ReactDOM: typeof import('react-dom');
    // limoBookingConfig is declared in src/types.d.ts
    limoBookingFormType?: BookingType;
    limoBookingStatus?: InitStatus;
    // Add limoBookingConfig?: any; ONLY if TypeScript complains it's not on window after this change.
  }
}

// Log initialization start
console.log('[LIMO BOOKING] Initialization script loaded');

// Main initialization function
export function initBookingForm(formTypeDefault: BookingType = 'point-to-point') {
  const formType = window.limoBookingFormType || formTypeDefault;
  console.log('Initializing Limo Booking Form...', { formType });

  // Check if container exists
  const container = document.getElementById('limo-booking-form');
  if (!container) {
    const error = 'Container element #limo-booking-form not found';
    console.error(error);
    (window as any).limoBookingStatus = { initialized: false, error };
    return;
  }

  // Check if React is available
  if (!(window as any).React) {
    const error = 'React is not loaded';
    console.error(error);
    (window as any).limoBookingStatus = { initialized: false, error };
    return;
  }

  // Check if ReactDOM is available
  if (!(window as any).ReactDOM) {
    const error = 'ReactDOM is not loaded';
    console.error(error);
    (window as any).limoBookingStatus = { initialized: false, error };
    return;
  }

  // Check if config is available
  const legacyConfig = (window.limoBookingConfig || {}) as any; // Cast to any for flexible transformation
  if (Object.keys(legacyConfig).length === 0 && window.limoBookingConfig === undefined) {
    const error = 'Limo Booking Config (window.limoBookingConfig) is not loaded or is empty';
    console.error(error);
    (window as any).limoBookingStatus = { initialized: false, error };
    return;
  }

  try {
    console.log('All checks passed, creating root...');
    const root = createRoot(container);

    // Transform LegacyConfig to BookingFormProps
    const props: BookingFormProps = {
      formType,
      apiKeys: {
        here: legacyConfig.hereApiKey || 'YOUR_INIT_HERE_API_KEY',
        mapbox: legacyConfig.mapboxToken || 'YOUR_INIT_MAPBOX_TOKEN',
        resend: legacyConfig.resendApiKey || 'YOUR_INIT_RESEND_API_KEY', 
        salesmate: legacyConfig.salesmateApiKey || 'YOUR_INIT_SALESMATE_API_KEY' 
      },
      nonce: legacyConfig.nonce || 'your_init_nonce_here',
      ajaxUrl: legacyConfig.ajaxUrl || '/wp-admin/admin-ajax.php',
      vehicleImages: legacyConfig.vehicleImages || { 
        sedan: 'path/to/init/sedan.jpg', 
        suv: 'path/to/init/suv.jpg'       
      }
    };

    root.render(React.createElement(BookingForm, props));
    console.log('Form initialized successfully');
    (window as any).limoBookingStatus = { initialized: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error initializing form:', errorMessage);
    (window as any).limoBookingStatus = { initialized: false, error: errorMessage };
  }
} 