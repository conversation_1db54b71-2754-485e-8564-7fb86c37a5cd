// @ts-nocheck
/// <reference path="../core/components/BookingForm/index.tsx" />
import React from 'react';
import ReactDOM from 'react-dom/client';
// Use type casting to avoid redeclaration conflicts
import * as BookingFormModule from '@core/components/BookingForm';
import { useBookingStore, BookingType } from '@core/store/bookingStore';
import { themeService } from '@core/services/themeService';
import '@core/styles/wordpress-index.css';
import './styles/bulletproof-plugin.css';
import { ErrorBoundary } from 'react-error-boundary';
import { scrollFormToTop } from '@core/lib/utils';
import eventManager from './eventManager';
import { MantineProvider, ColorSchemeScript } from '@mantine/core';
import { wordPressMantineTheme } from '@core/theme/wordPressMantineTheme';

// Extract BookingForm component with type casting
const { BookingForm } = BookingFormModule;

// Utility function to determine if a color is light or dark
const isLightColor = (color: string): boolean => {
  // Remove # if present
  const hex = color.replace('#', '');

  // Convert to RGB
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  // Calculate brightness using the standard formula
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;

  // Return true if brightness is greater than 128 (light color)
  return brightness > 128;
};

// Initialize event manager
eventManager.initialize();

// Define a global flag for scroll prevention
(window as any).preventFormScrolling = false;

// Set initial state based on URL hash
if (window.location.hash) {
  (window as any).preventFormScrolling = true;
}

// Update the global flag when event manager prevents scrolling
document.addEventListener('hashchange', () => {
  if (window.location.hash) {
    (window as any).preventFormScrolling = true;
  } else {
    (window as any).preventFormScrolling = false;
  }
});

// Create custom events for scroll prevention state changes
const createScrollPreventionEvent = (prevented: boolean) => {
  const eventName = prevented ? 'limo-booking-scroll-prevented' : 'limo-booking-scroll-allowed';
  const event = new CustomEvent(eventName, { bubbles: true });
  document.dispatchEvent(event);
};

// Initialize scroll prevention state based on URL hash
if (window.location.hash) {
  createScrollPreventionEvent(true);
}

// Function to apply WordPress theme without involving themeService
const applyWordPressThemeDirectly = (wpConfig: any) => {
  if (!wpConfig) {
    console.error('No WordPress theme config found');
    return;
  }

  // Mark that we're directly applying a WordPress theme
  (window as any).isDirectlyApplyingTheme = true;

  try {
    // Create a style element for our theme
    const styleId = 'limo-booking-theme';
    let styleEl = document.getElementById(styleId) as HTMLStyleElement;
    if (!styleEl) {
      styleEl = document.createElement('style');
      styleEl.id = styleId;
      document.head.appendChild(styleEl);
    }

    // BULLETPROOF CSS ARCHITECTURE - Clean theme variable injection
    const css = `
      :root {
        /* WordPress theme integration variables */
        --wp-limo-primary: ${wpConfig.primary_color};
        --wp-limo-primary-light: ${wpConfig.primary_light};
        --wp-limo-primary-dark: ${wpConfig.primary_dark};
        --wp-limo-background: ${wpConfig.background};
        --wp-limo-surface: ${wpConfig.surface};
        --wp-limo-surface-dark: ${wpConfig.surface_dark};
        --wp-limo-text-primary: ${wpConfig.text_primary};
        --wp-limo-text-secondary: ${wpConfig.text_secondary};
        --wp-limo-text-muted: ${wpConfig.text_muted};
        --wp-limo-text-disabled: ${wpConfig.text_disabled};
        --wp-limo-border: #333333;
        --wp-map-style: ${wpConfig.mapStyle};
      }
    `;

    // Calculate and add transparent variant
    const rgbBase = wpConfig.primary_color.match(/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
    if (rgbBase) {
      const r = parseInt(rgbBase[1], 16);
      const g = parseInt(rgbBase[2], 16);
      const b = parseInt(rgbBase[3], 16);
      const transparentRule = `
        :root {
          --wp-limo-primary-transparent: rgba(${r}, ${g}, ${b}, 0.5);
        }
      `;
      styleEl.textContent = css + transparentRule;
    } else {
      styleEl.textContent = css;
    }

    // Silently update the theme config (without triggering themeService)
    if (window.limoBookingConfig) {
      window.limoBookingConfig.theme = {
        ...window.limoBookingConfig.theme,
        ...wpConfig,
        // Ensure mapStyle is properly formatted
        mapStyle: wpConfig.mapStyle.replace(/\\\//g, '/').replace(/^"(.*)"$/, '$1').trim()
      };
    }
  } finally {
    // Always clear the direct application flag
    (window as any).isDirectlyApplyingTheme = false;
  }
};

// Main initialization function that can be called in normal or compatibility mode
const initBookingForm = () => {
  try {
    console.log('WordPress: initBookingForm called');
    const settings = (window as any).limoBookingSettings || {};
    console.log('WordPress: limoBookingSettings:', settings);

    if (!settings.theme && (window as any).limoBookingConfig) {
      settings.theme = (window as any).limoBookingConfig.theme;
      console.log('WordPress: Using theme from limoBookingConfig:', settings.theme);
    }

    // Apply theme as early as possible
    if (settings.theme) {
      console.log('WordPress: Applying theme directly:', settings.theme);
      applyWordPressThemeDirectly(settings.theme);
    }

    // Look for all booking form containers
    const containers = document.querySelectorAll('.limo-booking-plugin');
    console.log('WordPress: Found containers with .limo-booking-plugin class:', containers.length, Array.from(containers));

    if (containers.length === 0) {
      // Fallback to looking for the default ID
      const defaultContainer = document.getElementById('limo-booking-form');
      console.log('WordPress: Looking for fallback container with ID limo-booking-form:', defaultContainer);
      if (defaultContainer) {
        console.log('WordPress: Found default container, adding class and initializing');
        defaultContainer.classList.add('limo-booking-plugin');
        return (window as any).limoBookingInit('limo-booking-form');
      } else {
        console.error('WordPress: No limo booking form containers found');
        return false;
      }
    }

    console.log(`WordPress: Found ${containers.length} containers to initialize`);

    // Initialize each container
    let successCount = 0;
    containers.forEach((container, index) => {
      const containerId = container.id || `limo-booking-form-${index}`;
      if (!container.id) {
        container.id = containerId;
      }

      const formType = container.getAttribute('data-form-type') ||
                      container.getAttribute('data-type') ||
                      'point-to-point';

      console.log(`WordPress: Initializing container ${index}:`, {
        id: containerId,
        formType,
        element: container
      });

      if ((window as any).limoBookingInit(containerId, formType)) {
        successCount++;
        console.log(`WordPress: Successfully initialized container ${containerId}`);
      } else {
        console.error(`WordPress: Failed to initialize container ${containerId}`);
      }
    });

    console.log(`WordPress: Initialization complete. ${successCount}/${containers.length} containers initialized successfully`);
    return successCount > 0;
  } catch (error) {
    console.error('WordPress: Error in initBookingForm:', error);
    return false;
  }
};

// Helper function to get form type from DOM or settings
const getFormType = (): BookingType => {
  const limoBookingForm = document.getElementById('limo-booking-form');
  const formTypeFromDOM = limoBookingForm?.getAttribute('data-form-type') || 'point-to-point';
  const settings = (window as any).limoBookingSettings || {};

  // Use form type from DOM attribute first, then settings, then default
  return (formTypeFromDOM || settings.formType || 'point-to-point') as BookingType;
};

// Add book now button handler
const handleBookNowButton = () => {
  // Find all book-now buttons
  const bookNowButtons = document.querySelectorAll('.book-now-button, .book-now, [data-action="book-now"]');

  if (bookNowButtons.length > 0) {

    bookNowButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();

        // Find the booking form
        const bookingForm = document.getElementById('limo-booking-form');
        if (bookingForm) {
          // Scroll to the form
          bookingForm.scrollIntoView({ behavior: 'smooth' });

          // Focus on the first input
          setTimeout(() => {
            const firstInput = bookingForm.querySelector('input, select, textarea') as HTMLElement;
            if (firstInput) {
              firstInput.focus();
            }
          }, 500);
        } else {
          // If no form on this page, go to the booking page
          window.location.href = '/booking/';
        }
      });
    });
  }
};

// Make sure we have global variables properly configured
const ensureWordPressGlobals = () => {
  // Make sure the settings are available
  if (!(window as any).limoBookingSettings) {
    (window as any).limoBookingSettings = {};
  }

  // Same for limoBookingConfig
  if (!(window as any).limoBookingConfig) {
    (window as any).limoBookingConfig = {};
  }
};

// Expose the initialization function globally so it can be called from outside
(window as any).initLimoBookingForm = initBookingForm;

// Also expose it as limoBookingInit for backward compatibility with WordPress shortcode
(window as any).limoBookingInit = (containerId?: string, formType?: string) => {
  // Find the container element with retry logic
  let container: HTMLElement | null = null;
  let attempts = 0;
  const maxAttempts = 10;

  const findContainer = () => {
    if (containerId) {
      container = document.getElementById(containerId);
    } else {
      // Look for any container with the class
      container = document.querySelector('.limo-booking-plugin') as HTMLElement;
    }
    return container !== null;
  };

  // Try to find the container immediately
  if (!findContainer()) {
    // Set up retry mechanism
    const retryInterval = setInterval(() => {
      attempts++;

      if (findContainer()) {
        clearInterval(retryInterval);
        initializeContainer();
      } else if (attempts >= maxAttempts) {
        clearInterval(retryInterval);
        return false;
      }
    }, 100);

    return true; // Return true to indicate we're trying
  }

  // Container found immediately
  return initializeContainer();

  function initializeContainer() {
    if (!container) return false;

    // Get form type from parameter, DOM attribute, or default
    const finalFormType = formType ||
                         container.getAttribute('data-form-type') ||
                         container.getAttribute('data-type') ||
                         'point-to-point';

    // Get settings from WordPress
    const config = (window as any).limoBookingConfig || {};
    const settings = {
      theme: config.theme || {},
      apiKeys: {
        mapbox: config.mapboxToken || '',
        here: config.hereApiKey || ''
      },
      vehicleImages: config.vehicleImages || {},
      isWordPress: config.isWordPress || true,
      hideThemeSelector: config.hideThemeSelector || false,
      inheritThemeColors: config.inheritThemeColors || false
    };

    // Apply theme if available
    if (settings.theme && Object.keys(settings.theme).length > 0) {
      applyWordPressThemeDirectly(settings.theme);
    }

    // Debug vehicle images
    console.log('WordPress: Vehicle images being passed:', settings.vehicleImages);
    console.log('WordPress: Full config:', config);
    console.log('WordPress: Full settings:', settings);

    try {
      // Determine color scheme based on WordPress theme background
      const backgroundColor = settings.theme?.background || '#000000';
      const isLightBackground = isLightColor(backgroundColor);
      const colorScheme = isLightBackground ? 'light' : 'dark';

      console.log('WordPress theme background:', backgroundColor, 'isLight:', isLightBackground, 'colorScheme:', colorScheme);

      // SIMPLIFIED BULLETPROOF APPROACH: Use regular container with enhanced CSS isolation
      const reactContainer = container;

      // Add bulletproof container classes
      reactContainer.className = `limo-booking-plugin-container limo-booking-isolated-wp-${Date.now()}`;
      reactContainer.setAttribute('data-theme', colorScheme);
      reactContainer.setAttribute('data-loading', 'false');

      // Inject bulletproof CSS with unique namespace
      const uniqueId = `limo-booking-styles-${Date.now()}`;
      if (!document.getElementById(uniqueId)) {
        const styleElement = document.createElement('style');
        styleElement.id = uniqueId;
        styleElement.textContent = `
          /* BULLETPROOF CSS ISOLATION - SIMPLIFIED APPROACH */
          .limo-booking-plugin-container.limo-booking-plugin-container.limo-booking-plugin-container {
            /* Force our styles with high specificity */
            all: initial !important;
            display: block !important;
            box-sizing: border-box !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
            font-size: 16px !important;
            line-height: 1.5 !important;
            color: ${settings.theme?.text_primary || '#ffffff'} !important;
            background: ${settings.theme?.background || '#000000'} !important;
            border-radius: 12px !important;
            overflow: hidden !important;
            width: 100% !important;
            min-height: 400px !important;
            position: relative !important;
            isolation: isolate !important;
            z-index: 1 !important;

            /* CSS Variables */
            --limo-primary: ${settings.theme?.primary_color || '#765a3d'};
            --limo-background: ${settings.theme?.background || '#000000'};
            --limo-text-primary: ${settings.theme?.text_primary || '#ffffff'};
            --limo-surface: ${settings.theme?.surface || '#141414'};
          }

          /* Force all child elements to use our styling */
          .limo-booking-plugin-container.limo-booking-plugin-container * {
            box-sizing: border-box !important;
            font-family: inherit !important;
          }

          /* Loading state */
          .limo-booking-plugin-container[data-loading="true"]::after {
            content: "Loading booking form..." !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background: var(--limo-background) !important;
            color: var(--limo-text-primary) !important;
            font-size: 16px !important;
            z-index: 10 !important;
          }
        `;
        document.head.appendChild(styleElement);
        console.log('WordPress: Bulletproof CSS injected with unique ID:', uniqueId);
      }

      // Create React root and render booking form
      const root = ReactDOM.createRoot(reactContainer);
      root.render(
        <React.StrictMode>
          <ColorSchemeScript defaultColorScheme={colorScheme} />
          <MantineProvider theme={wordPressMantineTheme} defaultColorScheme={colorScheme}>
            <ErrorBoundary
              FallbackComponent={({ error }) => (
                <div className="limo-error-boundary" style={{
                  padding: '20px',
                  border: '2px solid #ef4444',
                  borderRadius: '8px',
                  background: '#fef2f2',
                  color: '#991b1b',
                  textAlign: 'center'
                }}>
                  <h3 style={{ margin: '0 0 16px 0', fontSize: '18px', fontWeight: '600' }}>Error Loading Booking Form</h3>
                  <p style={{ margin: '0 0 16px 0', fontSize: '14px' }}>{error.message}</p>
                  <button
                    onClick={() => window.location.reload()}
                    style={{
                      padding: '8px 16px',
                      background: '#ef4444',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '14px'
                    }}
                  >
                    Reload Page
                  </button>
                </div>
              )}
            >
              <BookingForm
                formType={finalFormType as any}
                apiKeys={settings.apiKeys || {}}
                nonce={config.nonce || ''}
                ajaxUrl={config.ajaxUrl || '/wp-admin/admin-ajax.php'}
                vehicleImages={settings.vehicleImages || {}}
                hideThemeSelector={settings.hideThemeSelector}
                inheritThemeColors={settings.inheritThemeColors}
              />
            </ErrorBoundary>
          </MantineProvider>
        </React.StrictMode>
      );

      // Mark container as ready
      container.setAttribute('data-ready', 'true');
      return true;
    } catch (error) {
      console.error('Error initializing booking form in container:', error);
      container.innerHTML = `
        <div style="padding: 20px; border: 2px solid #f44336; background: #ffebee; color: #b71c1c;">
          <h3>Error Loading Booking Form</h3>
          <p>Failed to initialize the booking form. Please refresh the page.</p>
          <button onclick="window.location.reload()" style="padding: 8px 16px; background: #f44336; color: white; border: none; cursor: pointer;">
            Reload Page
          </button>
        </div>
      `;
      return false;
    }
  }
};

// Safe initialization that wraps all operations in try/catch
const safeInitialize = () => {
  try {
    console.log('WordPress: Starting safe initialization...');
    ensureWordPressGlobals();
    handleBookNowButton();

    // Also try to initialize any existing forms
    setTimeout(() => {
      console.log('WordPress: Attempting to discover and initialize forms...');
      const result = initBookingForm();
      console.log('WordPress: Form initialization result:', result);
    }, 100);

    return true;
  } catch (error) {
    console.error('Error during initialization:', error);
    return false;
  }
};

// Initialize base functionality when document is ready
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  console.log('WordPress: Document already ready, initializing immediately');
  setTimeout(safeInitialize, 1);
} else {
  console.log('WordPress: Waiting for DOMContentLoaded');
  // Otherwise wait for DOMContentLoaded
  document.addEventListener('DOMContentLoaded', () => {
    console.log('WordPress: DOMContentLoaded fired, initializing...');
    safeInitialize();
  });
}

// Also provide a way to manually trigger form discovery if needed
(window as any).discoverAndInitializeForms = () => {
  return initBookingForm();
};

// Register global error handler
const errorHandler = (event: ErrorEvent) => {
  console.error('Global error caught:', event.error);

  // Try to find the booking form
  const bookingForm = document.getElementById('limo-booking-form');
  if (bookingForm) {
    // Only handle errors that are likely from our application
    const errorText = event.message || '';
    const isReactError =
      errorText.includes('React') ||
      errorText.includes('booking') ||
      errorText.includes('component') ||
      (event.filename && event.filename.includes('wordpress.js')) ||
      (event.error && event.error.stack && event.error.stack.includes('BookingForm'));

    if (isReactError) {
      bookingForm.innerHTML = `
        <div style="padding: 20px; border: 2px solid #f44336; background: #ffebee; color: #b71c1c;">
          <h3>Booking Form Error</h3>
          <p>${event.message}</p>
          <button onclick="window.location.reload()" style="padding: 8px 16px; background: #f44336; color: white; border: none; cursor: pointer;">
            Reload Page
          </button>
        </div>
      `;
    }
  }
};

// Register the error handler
window.addEventListener('error', errorHandler);

// Handle MapBox errors
const handleMapBoxErrors = () => {
  let checker = setInterval(() => {
    const mapboxError = document.querySelector('.mapboxgl-ctrl-attrib-inner a[href*="feedback"]');
    if (mapboxError) {
      clearInterval(checker);

      // Find the closest container
      const mapContainer = mapboxError.closest('.mapboxgl-map');
      if (mapContainer) {

        // Create a div to show error
        const errorDiv = document.createElement('div');
        errorDiv.className = 'mapbox-error-message';
        errorDiv.innerHTML = `
          <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.8); color: white; display: flex; align-items: center; justify-content: center; z-index: 10;">
            <div style="text-align: center; padding: 20px;">
              <h3>Map Loading Error</h3>
              <p>We couldn't load the map correctly. This might be due to an ad blocker or network issue.</p>
              <button onclick="window.location.reload()" style="padding: 8px 16px; background: #f44336; color: white; border: none; cursor: pointer; margin-top: 10px;">
                Reload Page
              </button>
            </div>
          </div>
        `;

        // Add to map container
        mapContainer.appendChild(errorDiv);
      }
    }
  }, 2000);

  // Clear the checker after 30 seconds
  setTimeout(() => {
    clearInterval(checker);
  }, 30000);
};

// Run MapBox error checker
setTimeout(handleMapBoxErrors, 5000);