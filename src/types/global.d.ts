declare global {
  interface Window {
    limoBookingConfig: {
      ajaxUrl?: string;
      restUrl?: string;
      apiBaseUrl?: string;
      nonce?: string;
      mapboxToken?: string;
      hereApiKey?: string;
      isWordPress?: boolean;
      hideThemeSelector?: boolean;
      theme?: {
        primary_color: string;
        primary_light: string;
        primary_dark: string;
        background: string;
        surface: string;
        surface_dark: string;
        text_primary: string;
        text_secondary: string;
        text_muted: string;
        text_disabled: string;
        mapTheme: 'dark' | 'light';
        mapStyle?: string;
      };
    };
    mapInstance?: {
      current: mapboxgl.Map | null;
      initialized: boolean;
      styleLoadPromise: Promise<void> | null;
    };
  }
}

export {}; 