import { useState } from 'react';
import { ThemeSelection } from './ThemeSelection';
import { SwatchIcon } from '@heroicons/react/24/outline';

export const BookingForm = () => {
  const [isThemeSelectionOpen, setIsThemeSelectionOpen] = useState(false);

  return (
    <>
      {/* Theme Button */}
      <button
        onClick={() => setIsThemeSelectionOpen(true)}
        className="fixed top-4 right-4 p-2 bg-surface rounded-lg shadow-lg hover:bg-surface-light transition-colors z-10"
        title="Change Theme"
      >
        <SwatchIcon className="w-6 h-6 text-primary" />
      </button>

      {/* Theme Selection Modal */}
      <ThemeSelection
        isOpen={isThemeSelectionOpen}
        onClose={() => setIsThemeSelectionOpen(false)}
      />

      {/* Existing Form Content */}
      // ... existing code ...
    </>
  );
}; 