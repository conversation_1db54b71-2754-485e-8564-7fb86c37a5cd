import React, { useState } from 'react';
import { useBookingStore } from '@core/store/bookingStore';
import { cn, scrollFormToTop } from '@core/lib/utils';
import { bookingDebugger } from '@core/utils/debugLogger';
import { Button, Grid, Paper, Text, Group, Badge, Image, Box } from '@mantine/core';
import { CheckCircleIcon } from '@heroicons/react/24/solid';

interface VehicleSelectionProps {
  vehicleImages?: Record<string, string>;
  onSelect?: (vehicleId: string) => void;
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

// Vehicle list that matches WordPress admin configuration exactly
const vehicles = [
  {
    id: 'sedan',
    name: 'Sedan',
    description: 'Leather seats, Tinted privacy glass, Premium sound system, Smooth and quiet ride',
    image: '/wp-content/uploads/limo-assets/vehicles/sedan.png',
    capacity: 3
  },
  {
    id: 'suv',
    name: 'SUV',
    description: 'Spacious interior, Leather seats, All-wheel drive, Premium sound system, Ample luggage space',
    image: '/wp-content/uploads/limo-assets/vehicles/suv.png',
    capacity: 6
  },
  {
    id: 'van',
    name: 'Van',
    description: 'Premium van with luxury seating, Entertainment system, Climate control, Perfect for groups',
    image: '/wp-content/uploads/limo-assets/vehicles/van.png',
    capacity: 12
  },
  {
    id: 'stretch_limo',
    name: 'Stretch Limo',
    description: 'Classic stretch limo with bar, entertainment system, mood lighting, Red carpet service',
    image: '/wp-content/uploads/limo-assets/vehicles/stretch_limo.png',
    capacity: 10
  },
  {
    id: 'party_bus',
    name: 'Party Bus',
    description: 'Ultimate party experience with dance floor, premium sound system, LED lighting, Bar service',
    image: '/wp-content/uploads/limo-assets/vehicles/party_bus.png',
    capacity: 20
  }
];

export const VehicleSelection: React.FC<VehicleSelectionProps> = ({
  vehicleImages = {},
  onSelect
}) => {
  // Debug vehicle images
  console.log('VehicleSelection: Received vehicleImages prop:', vehicleImages);
  console.log('VehicleSelection: Vehicle IDs we expect:', vehicles.map(v => v.id));
  const {
    formType,
    pointToPointData,
    hourlyData,
    airportData,
    multiDayData,
    updatePointToPointData,
    updateHourlyData,
    updateAirportData,
    updateMultiDayData,
    setStep,
    currentStep
  } = useBookingStore();

  // Get the correct data based on form type
  const bookingData = {
    'point-to-point': pointToPointData,
    'hourly': hourlyData,
    'airport': airportData,
    'multi-day': multiDayData
  }[formType];

  // Get the correct update function based on form type
  const updateData = {
    'point-to-point': updatePointToPointData,
    'hourly': updateHourlyData,
    'airport': updateAirportData,
    'multi-day': updateMultiDayData
  }[formType];

  const totalPassengers = (bookingData?.adults || 1) + (bookingData?.children || 0);

  const handleVehicleSelect = (vehicleId: string) => {
    bookingDebugger.log('debug', 'Vehicle', `Selected vehicle: ${vehicleId}`, {
      formType,
      previousVehicle: bookingData?.selectedVehicle
    });
    updateData({ selectedVehicle: vehicleId });
  };

  const handleNext = () => {
    if (bookingData?.selectedVehicle) {
      bookingDebugger.log('info', 'Navigation', 'Moving to confirmation step', {
        formType,
        selectedVehicle: bookingData.selectedVehicle
      });

      if (onSelect) {
        onSelect(bookingData.selectedVehicle);
      } else {
        setStep(currentStep + 1);
        setTimeout(() => scrollFormToTop(), 10);
      }
    }
  };

  const handleBack = () => {
    bookingDebugger.log('info', 'Navigation', 'Moving back to form step');
    setStep(currentStep - 1);
    setTimeout(() => scrollFormToTop(), 10);
  };

  // Update image paths with vehicleImages if provided
  const vehicleList = vehicles.map(vehicle => ({
    ...vehicle,
    image: vehicleImages[vehicle.id] || vehicle.image
  }));

  // Debug final vehicle list
  console.log('VehicleSelection: Final vehicle list with images:', vehicleList.map(v => ({
    id: v.id,
    name: v.name,
    image: v.image,
    hasCustomImage: !!vehicleImages[v.id]
  })));

  return (
    <Box mb="xl">
      <Text size="xl" fw={300} c="white" mb="lg">Select Your Vehicle</Text>

      <Grid gutter="md">
        {vehicleList.map((vehicle) => {
          const isSelected = bookingData?.selectedVehicle === vehicle.id;
          const isDisabled = totalPassengers > vehicle.capacity;

          return (
            <Grid.Col span={6} key={vehicle.id}>
              <Paper
                p="md"
                radius="md"
                onClick={() => !isDisabled && handleVehicleSelect(vehicle.id)}
                style={{
                  cursor: isDisabled ? 'not-allowed' : 'pointer',
                  opacity: isDisabled ? 0.5 : 1,
                  backgroundColor: isSelected ? 'var(--mantine-color-primary)' : 'var(--mantine-color-dark-6)',
                  color: isSelected ? 'white' : undefined,
                  border: isSelected ? 'none' : '1px solid var(--mantine-color-dark-4)',
                  transition: 'all 0.2s ease'
                }}
              >
                <Box pos="relative" mb="md">
                  <Box
                    style={{
                      aspectRatio: '16/9',
                      backgroundColor: 'var(--mantine-color-dark-7)',
                      borderRadius: 'var(--mantine-radius-md)',
                      overflow: 'hidden',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <Box style={{ maxWidth: '300px', width: '100%', margin: '0 auto' }}>
                      <Image
                        src={vehicle.image}
                        alt={vehicle.name}
                        fit="contain"
                        fallbackSrc="/wp-content/uploads/limo-assets/vehicles/default-vehicle.png"
                      />
                    </Box>
                  </Box>

                  {/* Passenger Capacity Badge */}
                  <Badge
                    pos="absolute"
                    top={8}
                    right={8}
                    color={isSelected ? 'blue' : 'gray'}
                    size="md"
                    leftSection={
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" style={{ width: '1rem', height: '1rem' }}>
                        <path d="M4.5 6.375a4.125 4.125 0 118.25 0 4.125 4.125 0 01-8.25 0zM14.25 8.625a3.375 3.375 0 116.75 0 3.375 3.375 0 01-6.75 0zM1.5 19.125a7.125 7.125 0 0114.25 0v.003l-.001.119a.75.75 0 01-.363.631 13.067 13.067 0 01-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 01-.364-.63l-.001-.122zM17.25 19.128l-.001.144a2.25 2.25 0 01-.233.96 10.088 10.088 0 005.06-1.01.75.75 0 00.42-.643 4.875 4.875 0 00-6.957-4.611 8.586 8.586 0 011.71 5.157v.003z" />
                      </svg>
                    }
                  >
                    {vehicle.capacity}
                  </Badge>
                </Box>

                <Text fw={500} mb="xs" size="lg">{vehicle.name}</Text>
                <Text
                  size="sm"
                  c={isSelected ? 'white' : 'dimmed'}
                  style={{ opacity: isSelected ? 0.8 : 1 }}
                >
                  {vehicle.description}
                </Text>
              </Paper>
            </Grid.Col>
          );
        })}
      </Grid>

      <Group justify="space-between" mt="xl">
        <Button variant="outline" onClick={handleBack}>
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!bookingData?.selectedVehicle}
        >
          Continue to Booking
        </Button>
      </Group>
    </Box>
  );
};
