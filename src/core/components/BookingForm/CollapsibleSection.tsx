import React from 'react';
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { cn } from '@core/lib/utils';

interface CollapsibleSectionProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  isUpdating?: boolean;
  titleClassName?: string;
  sectionId?: string;
  isOpen?: boolean;
  onToggle?: (sectionId: string) => void;
}

export const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  children,
  defaultOpen = false,
  isUpdating = false,
  titleClassName = '',
  sectionId = '',
  isOpen: controlledIsOpen,
  onToggle
}) => {
  const [internalIsOpen, setInternalIsOpen] = React.useState(defaultOpen);

  // Use controlled or uncontrolled state based on props
  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;

  const handleToggle = (e: React.MouseEvent) => {
    // Stop propagation to prevent the backdrop click handler from firing
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    
    if (onToggle && sectionId) {
      onToggle(sectionId);
    } else {
      setInternalIsOpen(!internalIsOpen);
    }
  };

  const handleContentClick = (e: React.MouseEvent) => {
    // Stop propagation to prevent the backdrop click handler from firing
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
  };

  return (
    <div 
      className={cn(
        "rounded-lg overflow-hidden transition-all duration-300",
        isUpdating ? "bg-primary/5" : "bg-surface"
      )}
      onClick={handleContentClick}
    >
      <button
        onClick={handleToggle}
        className={cn(
          "w-full flex items-center justify-between p-4",
          "transition-colors duration-300",
          isUpdating ? "bg-primary/10" : "hover:bg-surface-light"
        )}
      >
        <span className={cn(
          "font-medium transition-colors duration-300",
          titleClassName || "text-text-primary",
          isUpdating && "text-text-primary"
        )}>
          {title}
        </span>
        <ChevronDownIcon
          className={cn(
            "w-5 h-5 transition-transform duration-300",
            isOpen ? "transform rotate-180" : "",
            "text-text-primary"
          )}
        />
      </button>
      <div className={cn(
        "transition-all duration-300 overflow-hidden",
        isOpen ? "max-h-[2000px] opacity-100" : "max-h-0 opacity-0"
      )}>
        <div className="p-4" onClick={handleContentClick}>
          {children}
        </div>
      </div>
    </div>
  );
}; 