import React, { useEffect, useRef, useState, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { useBookingStore } from '@core/store/bookingStore';
import { config } from '@core/config/env';
import { Location, Stop } from '@core/types/booking';
import { bookingDebugger } from '@core/utils/debugLogger';
import { SwatchIcon } from '@heroicons/react/24/outline';
import { useThemeStore } from '@core/store/themeStore';

// Add AirportData type import
import type { AirportData } from '@core/store/bookingStore';

// Add marker styles at the top
const markerStyles = `
  .marker {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 14px;
    border: 2px solid var(--text-primary);
    box-shadow: 0 2px 4px var(--shadow);
    transition: transform 0.2s;
  }
  .marker:hover {
    transform: scale(1.1);
  }
  .pickup-marker {
    background-color: var(--success);
  }
  .stop-marker {
    background-color: var(--primary-transparent);
  }
  .dropoff-marker {
    background-color: var(--error);
  }
`;

interface BookingLocation {
  pickupLocation: Location | null;
  dropoffLocation?: Location | null;
  stops?: Stop[];
}

// Add error boundary wrapper with enhanced error logging
const withMapErrorBoundary = (WrappedComponent: React.ComponentType) => {
  return class MapErrorBoundary extends React.Component<{}, { hasError: boolean; errorInfo: string }> {
    constructor(props: {}) {
      super(props);
      this.state = { hasError: false, errorInfo: '' };
    }

    static getDerivedStateFromError(error: Error) {
      return { hasError: true, errorInfo: error.message };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
      bookingDebugger.log('error', 'Map', 'Map component error:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString()
      });
    }

    render() {
      if (this.state.hasError) {
        return (
          <div className="w-full h-[300px] rounded-lg bg-surface flex flex-col items-center justify-center p-4">
            <p className="text-disabled text-center mb-2">Unable to load map. Please check your connection.</p>
            <p className="text-xs text-disabled/70">{this.state.errorInfo}</p>
          </div>
        );
      }
      return <WrappedComponent {...this.props} />;
    }
  };
};

// Enhanced CSS variable helper with error handling
const getCSSVariableValue = (variableName: string) => {
  try {
    const value = getComputedStyle(document.documentElement).getPropertyValue(variableName).trim();
    if (!value) {
      bookingDebugger.log('warn', 'Theme', `Missing CSS variable: ${variableName}`);
      return null;
    }
    if (value.startsWith('#')) return value;
    const rgb = value.match(/\d+/g);
    if (rgb) return `rgb(${rgb.slice(0, 3).join(',')})`;
    return value;
  } catch (error) {
    bookingDebugger.log('error', 'Theme', `Error getting CSS variable ${variableName}:`, error);
    return null;
  }
};

// Update the getMapStyle function
const getMapStyle = () => {
  try {
    // First, try to get the style from CSS variable
    const cssMapStyle = getComputedStyle(document.documentElement).getPropertyValue('--map-style').trim();
    if (cssMapStyle && cssMapStyle !== '') {
      console.log('MapComponent: Using CSS variable map style:', cssMapStyle);
      return cssMapStyle;
    }
    
    // Get WordPress config
    const wpConfig = window.limoBookingConfig?.theme;

    // Log the full WordPress configuration once
    if (process.env.NODE_ENV === 'development') {
      console.log('MapComponent: Theme Configuration:', {
        fullConfig: window.limoBookingConfig,
        theme: wpConfig,
        mapStyle: wpConfig?.mapStyle,
        mapTheme: wpConfig?.mapTheme,
        timestamp: new Date().toISOString()
      });
    }

    // For WordPress, use the mapStyle from config
    if (wpConfig?.mapStyle) {
      // Clean up the style URL by:
      // 1. Removing escaped slashes
      // 2. Removing any wrapping quotes
      // 3. Trimming whitespace
      const cleanStyle = wpConfig.mapStyle
        .replace(/\\\//g, '/') // Replace escaped slashes
        .replace(/^"(.*)"$/, '$1') // Remove any wrapping quotes
        .replace(/\\"/g, '"') // Remove escaped quotes
        .replace(/\s+/g, '') // Remove all whitespace
        .trim();

      if (process.env.NODE_ENV === 'development') {
        console.log('MapComponent: Using WordPress map style:', cleanStyle);
      }
      return cleanStyle;
    }

    // For standalone, use the theme store
    const { mapTheme } = useThemeStore.getState();
    const style = mapTheme === 'dark'
      ? 'mapbox://styles/mapbox/dark-v11'
      : 'mapbox://styles/mapbox/light-v11';

    if (process.env.NODE_ENV === 'development') {
      console.log('MapComponent: Using standalone map style:', { mapTheme, style });
    }
    return style;
  } catch (error) {
    console.error('MapComponent: Error in getMapStyle:', error);
    return 'mapbox://styles/mapbox/light-v11'; // Safe fallback
  }
};

// Update transformRequest to fix credentials type and handle network errors
const transformRequest = (url: string, resourceType: string) => {
  try {
    // Skip certain requests that might be blocked
    if (url.includes('events.mapbox.com') ||
      url.includes('api.mapbox.com/events') ||
      url.includes('tiles.mapbox.com/v4/events')) {
      // Return null to cancel the request entirely
      return { url: '' };
    }

    // Allow all other Mapbox requests
    if (url.includes('mapbox.com')) {
      return {
        url,
        credentials: 'same-origin' as const // Fix the type issue
      };
    }

    return { url };
  } catch (error) {
    bookingDebugger.log('error', 'Network', 'Error in transformRequest:', error);
    // Return empty URL to prevent further errors
    return { url: '' };
  }
};

// Add this custom error handler for network requests
const setupNetworkErrorHandling = (map: mapboxgl.Map) => {
  try {
    // Need to access internal property with type assertion
    const mapAny = map as any;

    if (!mapAny._requestManager || !mapAny._requestManager.getJSON) {
      bookingDebugger.log('warn', 'Map', 'Cannot access _requestManager or getJSON method');
      return;
    }

    // Store the original getJSON method
    const originalGetJSON = mapAny._requestManager.getJSON;

    // Override the original getJSON method to add error handling
    mapAny._requestManager.getJSON = function (url: string, callback: Function) {
      try {
        // Skip certain requests that might be blocked
        if (url.includes('events.mapbox.com') ||
          url.includes('api.mapbox.com/events') ||
          url.includes('tiles.mapbox.com/v4/events')) {
          // Call callback with empty response to prevent errors
          setTimeout(() => callback(null, { data: {} }), 0);
          return;
        }

        // Add network request monitoring
        const id = Math.random().toString(36).substring(2, 9);
        bookingDebugger.log('debug', 'Network', `Starting map request [${id}]: ${url}`);

        originalGetJSON(url, (error: any, response: any) => {
          if (error) {
            bookingDebugger.log('error', 'Network', `Map request failed [${id}]: ${url}`, error);
          } else {
            bookingDebugger.log('debug', 'Network', `Map request succeeded [${id}]: ${url}`);
          }
          callback(error, response);
        });
      } catch (error) {
        // Log the error but prevent it from crashing
        bookingDebugger.log('error', 'Network', `Map request error: ${url}`, error);
        // Call callback with null error and empty response to avoid breaking the map
        callback(null, { data: {} });
      }
    };
  } catch (error) {
    bookingDebugger.log('error', 'Map', 'Error setting up network error handling:', error);
  }
};

// Add network monitoring types
interface NetworkRequest {
  url: string;
  startTime: number;
  endTime?: number;
  status?: number;
  error?: string;
  retryCount?: number;
}

interface NetworkStats {
  requests: Record<string, NetworkRequest>;
  failedRequestUrls: string[];
  totalRequests: number;
  successfulRequests: number;
  failedRequestCount: number;
  averageLatency: number;
}

// Add this type definition at the top with other interfaces
interface MapboxRequestParams {
  url: string;
  headers?: { [key: string]: string };
  credentials?: 'include' | 'same-origin';
}

// Set Mapbox token at the top level
if (!mapboxgl.accessToken) {
  mapboxgl.accessToken = config.mapboxToken;
}

// Add safe logging helper at the top
const safeLogMapObject = (obj: any) => {
  const safeObj: any = {};
  try {
    Object.keys(obj).forEach(key => {
      if (typeof obj[key] !== 'object' || obj[key] === null) {
        safeObj[key] = obj[key];
      } else if (Array.isArray(obj[key])) {
        safeObj[key] = '[Array]';
      } else if (obj[key] instanceof HTMLElement) {
        safeObj[key] = '[HTMLElement]';
      } else if (obj[key] instanceof WebGLRenderingContext) {
        safeObj[key] = '[WebGLContext]';
      } else {
        safeObj[key] = '[Object]';
      }
    });
    return safeObj;
  } catch (error) {
    return '[Unserializable Object]';
  }
};

// Update the map instance type
type MapInstance = mapboxgl.Map;

// Add a retry utility for style loading
const retryWithBackoff = async (
  fn: () => Promise<any>,
  retries = 3,
  baseDelay = 1000,
  maxDelay = 5000
): Promise<any> => {
  try {
    return await fn();
  } catch (error) {
    if (retries <= 0) {
      throw error;
    }

    // Calculate delay with exponential backoff (capped at maxDelay)
    const delay = Math.min(baseDelay * Math.pow(2, 3 - retries), maxDelay);

    bookingDebugger.log('warn', 'Map', `Retrying operation (${retries} attempts left)`, {
      delay,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });

    // Wait before retry
    await new Promise(resolve => setTimeout(resolve, delay));

    // Retry with one less retry attempt
    return retryWithBackoff(fn, retries - 1, baseDelay, maxDelay);
  }
};

// Add more detailed debugging utilities
const debugMap = {
  logEvent: (event: string, details: any = {}) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🗺️ [MAP:${event}]`, {
        ...details,
        timestamp: new Date().toISOString(),
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
          isMobile: window.innerWidth <= 768,
          orientation: window.orientation || 'undefined',
          userAgent: navigator.userAgent
        }
      });
    }

    // Also log to booking debugger for persistent logs
    bookingDebugger.log('debug', 'Map', event, {
      ...details,
      timestamp: new Date().toISOString()
    });
  },

  logError: (event: string, error: any) => {
    // Properly stringify the error object
    let errorMsg, stack;

    if (error instanceof Error) {
      errorMsg = error.message;
      stack = error.stack || 'No stack trace';
    } else if (typeof error === 'object' && error !== null) {
      // Handle object errors (like our custom timeout error)
      try {
        errorMsg = error.message || JSON.stringify(error);
        stack = error.stack || 'No stack trace';
      } catch (e) {
        errorMsg = 'Unserializable error object';
        stack = 'No stack trace';
      }
    } else {
      errorMsg = String(error);
      stack = 'No stack trace';
    }

    console.error(`🗺️ [MAP:ERROR:${event}]`, {
      error: errorMsg,
      stack,
      details: typeof error === 'object' && error !== null ? error : {},
      timestamp: new Date().toISOString()
    });

    bookingDebugger.log('error', 'Map', event, {
      error: errorMsg,
      stack,
      details: typeof error === 'object' && error !== null ? error : {},
      timestamp: new Date().toISOString()
    });
  },

  // New method to log container visibility and dimensions
  logContainerState: (ref: React.RefObject<HTMLDivElement>, context: string) => {
    if (!ref.current) {
      debugMap.logEvent(`CONTAINER_CHECK:${context}`, { status: 'missing' });
      return;
    }

    // Get computed styles
    const computedStyle = window.getComputedStyle(ref.current);
    const parentStyle = ref.current.parentElement ?
      window.getComputedStyle(ref.current.parentElement) :
      'no_parent';

    // Check visibility
    const isVisible = !(
      computedStyle.display === 'none' ||
      computedStyle.visibility === 'hidden' ||
      parseFloat(computedStyle.opacity) === 0
    );

    // Check if element is in DOM flow and has dimensions
    const hasLayout = ref.current.offsetWidth > 0 && ref.current.offsetHeight > 0;

    // Check if element is in viewport
    const rect = ref.current.getBoundingClientRect();
    const isInViewport = !(
      rect.bottom < 0 ||
      rect.right < 0 ||
      rect.top > window.innerHeight ||
      rect.left > window.innerWidth
    );

    debugMap.logEvent(`CONTAINER_CHECK:${context}`, {
      isVisible,
      hasLayout,
      isInViewport,
      dimensions: {
        clientWidth: ref.current.clientWidth,
        clientHeight: ref.current.clientHeight,
        offsetWidth: ref.current.offsetWidth,
        offsetHeight: ref.current.offsetHeight,
        scrollWidth: ref.current.scrollWidth,
        scrollHeight: ref.current.scrollHeight,
        boundingRect: {
          top: rect.top,
          right: rect.right,
          bottom: rect.bottom,
          left: rect.left,
          width: rect.width,
          height: rect.height
        }
      },
      styles: {
        display: computedStyle.display,
        visibility: computedStyle.visibility,
        opacity: computedStyle.opacity,
        position: computedStyle.position,
        overflow: computedStyle.overflow,
        zIndex: computedStyle.zIndex,
        transform: computedStyle.transform,
        width: computedStyle.width,
        height: computedStyle.height
      },
      parentStyles: parentStyle !== 'no_parent' ? {
        display: parentStyle.display,
        visibility: parentStyle.visibility,
        position: parentStyle.position,
        overflow: parentStyle.overflow
      } : 'no_parent'
    });
  },

  // New method to log WebGL context state
  logMapRenderState: (map: mapboxgl.Map | null, context: string) => {
    if (!map) {
      debugMap.logEvent(`RENDER_STATE:${context}`, { status: 'no_map_instance' });
      return;
    }

    try {
      // Get internal canvas
      const canvas = map.getCanvas();
      const container = map.getContainer();

      // Get WebGL state if possible
      let glContext = null;
      try {
        // @ts-ignore - accessing internal property
        const gl = map._context?.gl || null;
        glContext = gl ? {
          isContextLost: gl.isContextLost(),
          drawingBufferWidth: gl.drawingBufferWidth,
          drawingBufferHeight: gl.drawingBufferHeight
        } : 'no_gl_context';
      } catch (e) {
        glContext = `error:${e instanceof Error ? e.message : String(e)}`;
      }

      // Get internal map state
      const mapState = {
        loaded: map.loaded(),
        styleLoaded: map.isStyleLoaded(),
        // @ts-ignore - accessing internal properties
        renderComplete: map._render?.done !== false,
        // @ts-ignore
        renderStarted: map._render?.started === true,
        // @ts-ignore
        fullyLoaded: map.style?.loaded() === true
      };

      // Get canvas state
      const canvasState = canvas ? {
        width: canvas.width,
        height: canvas.height,
        clientWidth: canvas.clientWidth,
        clientHeight: canvas.clientHeight,
        style: {
          width: canvas.style.width,
          height: canvas.style.height,
          display: window.getComputedStyle(canvas).display
        }
      } : 'no_canvas';

      debugMap.logEvent(`RENDER_STATE:${context}`, {
        mapState,
        canvasState,
        glContext,
        containerState: container ? {
          offsetWidth: container.offsetWidth,
          offsetHeight: container.offsetHeight,
          clientWidth: container.clientWidth,
          clientHeight: container.clientHeight
        } : 'no_container'
      });
    } catch (error) {
      debugMap.logError(`RENDER_STATE:${context}`, error);
    }
  }
};

// Add interface before the component
interface ExtendedAirportData extends AirportData {
  stops?: Stop[];
}

// Add this mapInstance utility object before the withMapErrorBoundary function
// This is a singleton utility that manages the mapboxgl map instance
const mapInstance = {
  current: null as mapboxgl.Map | null,
  initialized: false,
  styleLoadRetries: 0,
  styleLoadTimeoutId: null as NodeJS.Timeout | null,

  // Initialize the map
  init(container: HTMLDivElement, onStyleLoad?: () => void): mapboxgl.Map | null {
    try {
      // Clean up any existing instance first
      this.destroy();

      debugMap.logEvent('MAP_INSTANCE_INIT_STARTED');

      // Create new map instance with interactive: false to disable all interactions
      this.current = new mapboxgl.Map({
        container,
        style: getMapStyle(),
        center: [-73.9856, 40.7497], // NYC default
        zoom: 12,
        attributionControl: false,
        trackResize: true,
        renderWorldCopies: false,
        transformRequest,
        fadeDuration: 0,
        interactive: false, // Disable all map interactions
        dragPan: false,    // Disable panning
        dragRotate: false, // Disable rotation
        scrollZoom: false, // Disable zoom
        doubleClickZoom: false, // Disable double click zoom
        collectResourceTiming: false, // Disable resource timing collection to prevent blocked requests
        refreshExpiredTiles: false, // Disable refreshing expired tiles to reduce network requests
        maxBounds: null, // No bounds restriction
        localIdeographFontFamily: "'Noto Sans', 'Noto Sans CJK SC', sans-serif" // Use local fonts when possible
      });

      // Setup custom network error handling
      setupNetworkErrorHandling(this.current);

      // Setup style loaded handler
      if (onStyleLoad) {
        this.current.once('style.load', () => {
          debugMap.logEvent('STYLE_LOADED_EVENT_FIRED');
          if (onStyleLoad) onStyleLoad();
        });
      }

      // Setup error handlers
      this.current.on('error', (e) => {
        debugMap.logError('MAP_INSTANCE_ERROR', e);
      });

      // Mark as initialized
      this.initialized = true;
      return this.current;
    } catch (error) {
      debugMap.logError('MAP_INSTANCE_INIT_ERROR', error);
      this.current = null;
      this.initialized = false;
      return null;
    }
  },

  // Clean up the map instance
  destroy() {
    if (this.styleLoadTimeoutId) {
      clearTimeout(this.styleLoadTimeoutId);
      this.styleLoadTimeoutId = null;
    }

    if (this.current) {
      try {
        debugMap.logEvent('MAP_INSTANCE_DESTROY_STARTED');
        this.current.remove();
        debugMap.logEvent('MAP_INSTANCE_DESTROY_COMPLETE');
      } catch (error) {
        debugMap.logError('MAP_INSTANCE_DESTROY_ERROR', error);
      }
    }

    this.current = null;
    this.initialized = false;
    this.styleLoadRetries = 0;
  },

  // Wait for style to load with timeout
  async waitForStyle(timeoutMs: number = 3000): Promise<void> {  // Reduced timeout for better reliability
    if (!this.current) {
      throw new Error('Cannot wait for style: map instance is null');
    }

    // If style is already loaded, resolve immediately
    if (this.current.isStyleLoaded()) {
      debugMap.logEvent('STYLE_ALREADY_LOADED');
      return Promise.resolve();
    }

    debugMap.logEvent('WAITING_FOR_STYLE_LOAD', {
      timeoutMs,
      retriesLeft: 3 - this.styleLoadRetries
    });

    return new Promise((resolve) => {
      let styleLoadHandler: (() => void) | null = null;
      let errorHandler: ((e: any) => void) | null = null;

      // Set a timeout
      this.styleLoadTimeoutId = setTimeout(() => {
        this.styleLoadRetries++;

        // Create a proper error object with message for better logging
        const timeoutError = {
          message: `Style load timed out after ${timeoutMs}ms (retry ${this.styleLoadRetries}/3)`,
          retryCount: this.styleLoadRetries,
          timeoutMs
        };

        debugMap.logEvent('STYLE_LOAD_TIMEOUT', timeoutError);

        // Clean up event handlers
        if (styleLoadHandler && this.current) {
          this.current.off('style.load', styleLoadHandler);
        }
        if (errorHandler && this.current) {
          this.current.off('error', errorHandler);
        }

        // Instead of rejecting, we'll resolve with a warning
        debugMap.logEvent('RESOLVING_DESPITE_STYLE_TIMEOUT', {
          retryCount: this.styleLoadRetries
        });
        resolve();
      }, timeoutMs);

      // Setup initial style.load handler
      styleLoadHandler = () => {
        if (this.styleLoadTimeoutId) {
          clearTimeout(this.styleLoadTimeoutId);
          this.styleLoadTimeoutId = null;
        }

        // Clean up error handler
        if (errorHandler && this.current) {
          this.current.off('error', errorHandler);
        }

        debugMap.logEvent('STYLE_LOADED_SUCCESS');
        resolve();
      };

      // Setup error handler for network errors
      errorHandler = (e: any) => {
        // Only handle network-related errors
        if (e.error && (
          (e.error.message && e.error.message.includes("Cannot read properties of undefined")) ||
          (e.error.status && e.error.status >= 400)
        )) {
          debugMap.logEvent('STYLE_LOAD_NETWORK_ERROR', {
            message: e.error.message,
            status: e.error.status,
            source: e.source
          });

          // If we get a network error, clear the timeout and resolve anyway
          if (this.styleLoadTimeoutId) {
            clearTimeout(this.styleLoadTimeoutId);
            this.styleLoadTimeoutId = null;
          }

          // Clean up event handlers
          if (styleLoadHandler && this.current) {
            this.current.off('style.load', styleLoadHandler);
          }
          if (this.current) {
            this.current.off('error', errorHandler);
          }

          // Resolve despite the error
          debugMap.logEvent('RESOLVING_DESPITE_NETWORK_ERROR');
          resolve();
        }
      };

      // Add event handlers
      this.current.once('style.load', styleLoadHandler);
      this.current.on('error', errorHandler);
    });
  }
};

// Add a key-based approach to force complete re-rendering when viewport changes
export const MapComponent = withMapErrorBoundary(() => {
  // Basic refs and state
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const markersRef = useRef<{ [key: string]: mapboxgl.Marker }>({});
  const { formType, pointToPointData, hourlyData, airportData } = useBookingStore();

  // Component state
  const [isStyleLoading, setIsStyleLoading] = useState(false);
  const [mapInitError, setMapInitError] = useState<string | null>(null);
  const [viewportWidth, setViewportWidth] = useState<number>(window.innerWidth);
  const [isMobileView, setIsMobileView] = useState<boolean>(window.innerWidth <= 768);
  const [mapKey, setMapKey] = useState<number>(Date.now()); // Key to force complete re-mount
  const [loadAttempts, setLoadAttempts] = useState<number>(0);

  // Refs for internal component state
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const previousWidthRef = useRef<number>(window.innerWidth);
  const mountedRef = useRef<boolean>(true);
  const componentMountTimestamp = useRef<string>(new Date().toISOString());

  // Get active data based on form type
  const getActiveData = (): BookingLocation => {
    switch (formType) {
      case 'point-to-point':
        return {
          pickupLocation: pointToPointData.pickupLocation,
          dropoffLocation: pointToPointData.dropoffLocation,
          stops: pointToPointData.stops || []
        };
      case 'hourly':
        return {
          pickupLocation: hourlyData.pickupLocation,
          dropoffLocation: hourlyData.dropoffLocation,
          stops: hourlyData.stops || []
        };
      case 'airport':
        const extendedAirportData = airportData as ExtendedAirportData;
        return {
          pickupLocation: extendedAirportData.pickupLocation,
          dropoffLocation: extendedAirportData.dropoffLocation,
          stops: extendedAirportData.stops || []
        };
      default:
        return {
          pickupLocation: null,
          dropoffLocation: null,
          stops: []
        };
    }
  };

  // Helper function to validate coordinates
  function isValidCoordinates(coords: any): coords is [number, number] {
    return Array.isArray(coords) &&
      coords.length === 2 &&
      typeof coords[0] === 'number' &&
      typeof coords[1] === 'number' &&
      !isNaN(coords[0]) &&
      !isNaN(coords[1]) &&
      Math.abs(coords[0]) <= 180 &&
      Math.abs(coords[1]) <= 90;
  }

  // Helper function to create markers with better error handling
  const createMarker = useCallback((type: 'pickup' | 'stop' | 'dropoff', label: string, coordinates: [number, number]) => {
    // Check if component is still mounted
    if (!mountedRef.current) {
      debugMap.logEvent(`${type.toUpperCase()}_MARKER_CREATION_ABORTED`, { reason: 'Component unmounted' });
      return null;
    }

    debugMap.logEvent(`CREATING_${type.toUpperCase()}_MARKER`, { label, coordinates });

    try {
      // Validate map instance
      if (!mapInstance.current) {
        debugMap.logEvent(`${type.toUpperCase()}_MARKER_CREATION_ABORTED`, { reason: 'Map instance is null' });
        return null;
      }

      // Validate coordinates again
      if (!isValidCoordinates(coordinates)) {
        debugMap.logEvent(`${type.toUpperCase()}_MARKER_INVALID_COORDINATES`, { coordinates });
        return null;
      }

      // Create and style the marker element
      const el = document.createElement('div');
      el.className = `marker ${type}-marker`;
      el.innerHTML = label;

      // Check if map is loaded before adding marker
      if (!mapInstance.current.loaded()) {
        debugMap.logEvent('MAP_NOT_LOADED_FOR_MARKER', {
          type,
          mapState: {
            loaded: mapInstance.current.loaded(),
            styleLoaded: mapInstance.current.isStyleLoaded()
          }
        });

        // We'll continue anyway, but log the warning
      }

      // Final check before creating marker
      if (!mapInstance.current || !mountedRef.current) {
        debugMap.logEvent(`${type.toUpperCase()}_MARKER_CREATION_ABORTED`, {
          reason: !mapInstance.current ? 'Map instance became null' : 'Component unmounted',
          mapExists: !!mapInstance.current,
          mounted: mountedRef.current
        });
        return null;
      }

      // Create the marker with try/catch for each step
      try {
        const marker = new mapboxgl.Marker(el);

        // Set coordinates
        try {
          marker.setLngLat(coordinates);
        } catch (coordError) {
          debugMap.logError(`${type.toUpperCase()}_MARKER_SETLNGLAT_ERROR`, coordError);
          return null;
        }

        // Add to map with final check
        if (!mapInstance.current) {
          debugMap.logEvent(`${type.toUpperCase()}_MARKER_ADDTO_ABORTED`, { reason: 'Map instance became null' });
          return null;
        }

        try {
          marker.addTo(mapInstance.current);
        } catch (addError) {
          debugMap.logError(`${type.toUpperCase()}_MARKER_ADDTO_ERROR`, addError);
          return null;
        }

        debugMap.logEvent(`${type.toUpperCase()}_MARKER_CREATED_SUCCESS`);
        return marker;
      } catch (markerError) {
        debugMap.logError(`${type.toUpperCase()}_MARKER_CREATION_ERROR`, markerError);
        return null;
      }
    } catch (error) {
      debugMap.logError(`${type.toUpperCase()}_MARKER_CREATION_ERROR`, error);
      return null;
    }
  }, []);

  // Helper function to draw route with enhanced error logging
  const drawRoute = useCallback(async () => {
    // Check if component is still mounted
    if (!mountedRef.current) {
      debugMap.logEvent('DRAW_ROUTE_ABORTED', { reason: 'Component unmounted' });
      return;
    }

    if (!mapInstance.current) {
      debugMap.logEvent('DRAW_ROUTE_DEFERRED', { reason: 'Map instance is null' });
      return;
    }

    try {
      // Wait for style to load before drawing route
      debugMap.logEvent('DRAW_ROUTE_WAITING_FOR_STYLE');
      try {
        await mapInstance.waitForStyle();
        debugMap.logEvent('DRAW_ROUTE_STYLE_READY');
      } catch (styleError) {
        // Continue even if style loading fails
        debugMap.logEvent('DRAW_ROUTE_CONTINUING_DESPITE_STYLE_ERROR');
      }

      // Check again if map instance still exists after waiting for style
      if (!mapInstance.current || !mountedRef.current) {
        debugMap.logEvent('DRAW_ROUTE_ABORTED_AFTER_STYLE_WAIT', {
          mapExists: !!mapInstance.current,
          mounted: mountedRef.current
        });
        return;
      }

      const { pickupLocation, dropoffLocation, stops } = getActiveData();

      // Collect all valid locations in order: pickup -> stops -> dropoff
      const validLocations: [number, number][] = [];

      // Add pickup location
      if (pickupLocation?.coordinates && isValidCoordinates(pickupLocation.coordinates)) {
        validLocations.push(pickupLocation.coordinates);
      }

      // Add stops in order
      if (stops && stops.length > 0) {
        stops.forEach(stop => {
          if (stop?.location?.coordinates && isValidCoordinates(stop.location.coordinates)) {
            validLocations.push(stop.location.coordinates);
          }
        });
      }

      // Add dropoff location
      if (dropoffLocation?.coordinates && isValidCoordinates(dropoffLocation.coordinates)) {
        validLocations.push(dropoffLocation.coordinates);
      }

      debugMap.logEvent('ROUTE_LOCATIONS_COLLECTED', {
        locationCount: validLocations.length,
        locations: validLocations
      });

      if (validLocations.length < 2) {
        debugMap.logEvent('INSUFFICIENT_ROUTE_LOCATIONS', { count: validLocations.length });
        return;
      }

      const coords = validLocations.map(coord => coord.join(',')).join(';');
      debugMap.logEvent('FETCHING_ROUTE', { coordinateString: coords });

      try {
        // Use a timeout to prevent hanging requests
        const controller = new AbortController();
        const response = await fetch(
          `https://api.mapbox.com/directions/v5/mapbox/driving/${coords}?geometries=geojson&access_token=${config.mapboxToken}`
        );

        if (!response.ok) {
          throw new Error(`Route API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        debugMap.logEvent('ROUTE_DATA_RECEIVED', {
          status: response.status,
          hasRoute: !!data.routes?.[0],
          distance: data.routes?.[0]?.distance,
          duration: data.routes?.[0]?.duration
        });

        // Check again if map instance still exists after fetching route data
        if (!mapInstance.current || !mountedRef.current) {
          debugMap.logEvent('ROUTE_DRAWING_ABORTED_AFTER_FETCH', {
            mapExists: !!mapInstance.current,
            mounted: mountedRef.current
          });
          return;
        }

        if (data.routes?.[0]) {
          // Remove existing route if any
          try {
            if (mapInstance.current.getLayer('route')) {
              mapInstance.current.removeLayer('route');
            }
            if (mapInstance.current.getSource('route')) {
              mapInstance.current.removeSource('route');
            }
            debugMap.logEvent('REMOVED_EXISTING_ROUTE');
          } catch (removeError) {
            debugMap.logError('ROUTE_REMOVAL_ERROR', removeError);
            // Continue anyway
          }

          // Final check before adding new route
          if (!mapInstance.current || !mountedRef.current) {
            debugMap.logEvent('ROUTE_DRAWING_ABORTED_AFTER_CLEANUP', {
              mapExists: !!mapInstance.current,
              mounted: mountedRef.current
            });
            return;
          }

          // Add new route
          try {
            mapInstance.current.addSource('route', {
              type: 'geojson',
              data: {
                type: 'Feature',
                properties: {},
                geometry: data.routes[0].geometry
              }
            });

            mapInstance.current.addLayer({
              id: 'route',
              type: 'line',
              source: 'route',
              layout: {
                'line-join': 'round',
                'line-cap': 'round'
              },
              paint: {
                'line-color': getCSSVariableValue('--primary') || '#3b82f6',
                'line-width': 4,
                'line-opacity': 0.75
              }
            });

            debugMap.logEvent('ROUTE_DRAWN_SUCCESSFULLY');
          } catch (drawError) {
            debugMap.logError('ROUTE_DRAWING_ERROR', drawError);
          }
        }
      } catch (fetchError) {
        debugMap.logError('ROUTE_FETCH_ERROR', fetchError);
      }
    } catch (routeError) {
      debugMap.logError('DRAW_ROUTE_ERROR', routeError);
    }
  }, [getActiveData]);

  // Now define updateMapMarkers after its dependencies
  const updateMapMarkers = useCallback(async () => {
    // Check if component is still mounted
    if (!mountedRef.current) {
      debugMap.logEvent('UPDATE_MARKERS_ABORTED', { reason: 'Component unmounted' });
      return;
    }

    // Check if map instance exists
    if (!mapInstance.current) {
      debugMap.logEvent('UPDATE_MARKERS_DEFERRED', { reason: 'Map instance is null' });

      // Instead of throwing an error, schedule a retry after a short delay
      setTimeout(() => {
        if (mountedRef.current && mapInstance.current) {
          debugMap.logEvent('UPDATE_MARKERS_RETRY');
          updateMapMarkers();
        }
      }, 500);

      return;
    }

    try {
      // Wait for style to load before updating markers
      if (isStyleLoading) {
        debugMap.logEvent('UPDATE_MARKERS_WAITING_FOR_STYLE');
        try {
          await mapInstance.waitForStyle();
          debugMap.logEvent('UPDATE_MARKERS_STYLE_READY');
        } catch (styleError) {
          // Continue even if style loading fails
          debugMap.logEvent('UPDATE_MARKERS_CONTINUING_DESPITE_STYLE_ERROR');
        }
      }

      // Check again if map instance still exists after waiting for style
      if (!mapInstance.current || !mountedRef.current) {
        debugMap.logEvent('UPDATE_MARKERS_ABORTED_AFTER_STYLE_WAIT', {
          mapExists: !!mapInstance.current,
          mounted: mountedRef.current
        });
        return;
      }

      debugMap.logEvent('UPDATE_MARKERS_STARTED');
      const { pickupLocation, dropoffLocation, stops } = getActiveData();
      const bounds = new mapboxgl.LngLatBounds();
      let hasValidLocations = false;
      const currentMarkerIds = new Set(Object.keys(markersRef.current));
      const newMarkerIds = new Set<string>();

      debugMap.logEvent('UPDATE_MARKERS_LOCATIONS', {
        pickup: pickupLocation?.coordinates,
        dropoff: dropoffLocation?.coordinates,
        stops: stops?.map(s => s?.location?.coordinates),
        markerCount: Object.keys(markersRef.current).length
      });

      // Update pickup marker
      if (pickupLocation?.coordinates && isValidCoordinates(pickupLocation.coordinates)) {
        const id = 'pickup';
        newMarkerIds.add(id);
        try {
          // Check map instance again before creating marker
          if (!mapInstance.current) {
            debugMap.logEvent('PICKUP_MARKER_CREATION_SKIPPED', { reason: 'Map instance became null' });
          } else {
            const marker = createMarker('pickup', 'A', pickupLocation.coordinates);
            if (marker) {
              if (markersRef.current[id]) {
                markersRef.current[id].remove();
              }
              markersRef.current[id] = marker;
              bounds.extend(pickupLocation.coordinates);
              hasValidLocations = true;
              debugMap.logEvent('PICKUP_MARKER_CREATED', { coordinates: pickupLocation.coordinates });
            } else {
              debugMap.logEvent('PICKUP_MARKER_CREATION_FAILED', 'Marker creation returned null');
            }
          }
        } catch (markerError) {
          debugMap.logError('PICKUP_MARKER_ERROR', markerError);
        }
      }

      // Update stop markers
      stops?.forEach((stop, index) => {
        if (stop?.location?.coordinates && isValidCoordinates(stop.location.coordinates)) {
          const id = `stop-${stop.id || index}`;
          newMarkerIds.add(id);
          try {
            // Check map instance again before creating marker
            if (!mapInstance.current) {
              debugMap.logEvent('STOP_MARKER_CREATION_SKIPPED', { index, reason: 'Map instance became null' });
            } else {
              const marker = createMarker('stop', `${index + 1}`, stop.location.coordinates);
              if (marker) {
                if (markersRef.current[id]) {
                  markersRef.current[id].remove();
                }
                markersRef.current[id] = marker;
                bounds.extend(stop.location.coordinates);
                hasValidLocations = true;
                debugMap.logEvent('STOP_MARKER_CREATED', {
                  index,
                  id: stop.id || index,
                  coordinates: stop.location.coordinates
                });
              } else {
                debugMap.logEvent('STOP_MARKER_CREATION_FAILED', { index, reason: 'Marker creation returned null' });
              }
            }
          } catch (markerError) {
            debugMap.logError('STOP_MARKER_ERROR', { index, error: markerError });
          }
        }
      });

      // Update dropoff marker
      if (dropoffLocation?.coordinates && isValidCoordinates(dropoffLocation.coordinates)) {
        const id = 'dropoff';
        newMarkerIds.add(id);
        try {
          // Check map instance again before creating marker
          if (!mapInstance.current) {
            debugMap.logEvent('DROPOFF_MARKER_CREATION_SKIPPED', { reason: 'Map instance became null' });
          } else {
            const marker = createMarker('dropoff', 'B', dropoffLocation.coordinates);
            if (marker) {
              if (markersRef.current[id]) {
                markersRef.current[id].remove();
              }
              markersRef.current[id] = marker;
              bounds.extend(dropoffLocation.coordinates);
              hasValidLocations = true;
              debugMap.logEvent('DROPOFF_MARKER_CREATED', { coordinates: dropoffLocation.coordinates });
            } else {
              debugMap.logEvent('DROPOFF_MARKER_CREATION_FAILED', 'Marker creation returned null');
            }
          }
        } catch (markerError) {
          debugMap.logError('DROPOFF_MARKER_ERROR', markerError);
        }
      }

      // Remove obsolete markers
      currentMarkerIds.forEach(id => {
        if (!newMarkerIds.has(id) && markersRef.current[id]) {
          try {
            markersRef.current[id].remove();
            delete markersRef.current[id];
            debugMap.logEvent('MARKER_REMOVED', { id });
          } catch (removeError) {
            debugMap.logError('MARKER_REMOVE_ERROR', { id, error: removeError });
          }
        }
      });

      debugMap.logEvent('MARKERS_UPDATED', {
        hasValidLocations,
        newMarkerCount: newMarkerIds.size,
        currentMarkerCount: Object.keys(markersRef.current).length
      });

      // Final check before fitting bounds
      if (!mapInstance.current || !mountedRef.current) {
        debugMap.logEvent('BOUNDS_FIT_ABORTED', {
          mapExists: !!mapInstance.current,
          mounted: mountedRef.current
        });
        return;
      }

      if (hasValidLocations) {
        debugMap.logEvent('FITTING_BOUNDS', {
          bounds: [
            [bounds.getWest(), bounds.getSouth()],
            [bounds.getEast(), bounds.getNorth()]
          ]
        });

        try {
          mapInstance.current.fitBounds(bounds, {
            padding: { top: 50, bottom: 50, left: 50, right: 50 },
            maxZoom: 15,
            duration: 500
          });
          debugMap.logEvent('BOUNDS_FIT_COMPLETE');

          // Only attempt to draw route if bounds were successfully fit
          // Check map instance again before drawing route
          if (mapInstance.current && mountedRef.current) {
            await drawRoute();
          } else {
            debugMap.logEvent('ROUTE_DRAWING_SKIPPED', {
              mapExists: !!mapInstance.current,
              mounted: mountedRef.current
            });
          }
        } catch (fitError) {
          debugMap.logError('FIT_BOUNDS_ERROR', fitError);
        }
      } else {
        debugMap.logEvent('NO_VALID_LOCATIONS');
      }
    } catch (error) {
      debugMap.logError('UPDATE_MARKERS_ERROR', error);
    }
  }, [isStyleLoading, createMarker, drawRoute, getActiveData]);

  // Log component mount
  useEffect(() => {
    debugMap.logEvent('COMPONENT_MOUNTED', {
      mapKey,
      viewportWidth,
      isMobileView,
      mountTimestamp: componentMountTimestamp.current
    });

    return () => {
      debugMap.logEvent('COMPONENT_UNMOUNTED', {
        mapKey,
        viewportWidth,
        isMobileView,
        mountDuration: new Date().getTime() - new Date(componentMountTimestamp.current).getTime()
      });
    };
  }, []);

  // Clean up function with enhanced logging
  const cleanUpMap = useCallback(() => {
    debugMap.logEvent('CLEANUP_START', {
      markerCount: Object.keys(markersRef.current).length,
      mapInitialized: mapInstance.initialized
    });

    // Log container state before cleanup
    debugMap.logContainerState(mapContainerRef, 'BEFORE_CLEANUP');
    // Log map render state before cleanup
    debugMap.logMapRenderState(mapInstance.current, 'BEFORE_CLEANUP');

    // Clean up all markers
    Object.values(markersRef.current).forEach(marker => {
      if (marker) marker.remove();
    });
    // Reset markersRef
    markersRef.current = {};

    // Destroy map instance
    mapInstance.destroy();

    // Log container state after cleanup
    debugMap.logContainerState(mapContainerRef, 'AFTER_CLEANUP');

    debugMap.logEvent('CLEANUP_COMPLETE');
  }, []);

  // Update the reinitializeMap function to handle transitions better
  const reinitializeMap = useCallback(() => {
    debugMap.logEvent('REINITIALIZE_MAP', {
      loadAttempts,
      mapKey,
      isMobileView
    });

    // Clean up existing map and markers
    cleanUpMap();

    // Force a complete component re-mount by changing the key
    const newKey = Date.now();
    setMapKey(newKey);

    // Reset retry counters
    setLoadAttempts(0);  // Reset attempts on manual reinitialize
    if (mapInstance) {
      mapInstance.styleLoadRetries = 0;
    }

    // Set loading state
    setIsStyleLoading(true);
    setMapInitError(null);  // Clear any previous errors

    // Add a small delay before reinitialization to ensure DOM is ready
    setTimeout(() => {
      if (mountedRef.current) {
        debugMap.logEvent('DELAYED_REINITIALIZE', {
          newMapKey: newKey,
          containerReady: !!mapContainerRef.current,
          dimensions: mapContainerRef.current ? {
            width: mapContainerRef.current.offsetWidth,
            height: mapContainerRef.current.offsetHeight
          } : null
        });
      }
    }, 100);

    debugMap.logEvent('REINITIALIZE_COMPLETE', {
      newMapKey: newKey,
      newLoadAttempts: 0
    });
  }, [cleanUpMap, mapKey, isMobileView]);

  // Track viewport width changes with a more stable approach
  useEffect(() => {
    const handleResize = () => {
      // Clear previous timeout if it exists
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }

      // Get current width
      const currentWidth = window.innerWidth;
      const wasMobile = previousWidthRef.current <= 768;
      const isMobileNow = currentWidth <= 768;
      const crossedBreakpoint = wasMobile !== isMobileNow;

      // Update state
      setViewportWidth(currentWidth);
      setIsMobileView(isMobileNow);

      // Handle the map resize
      if (mapInstance.current) {
        try {
          // First update the container dimensions
          if (mapContainerRef.current) {
            // Force container to be visible and have dimensions before any map operations
            mapContainerRef.current.style.display = 'block';
            mapContainerRef.current.style.visibility = 'visible';
            mapContainerRef.current.style.height = isMobileNow ? '250px' : '300px';
            mapContainerRef.current.style.minHeight = isMobileNow ? '250px' : '300px';
            mapContainerRef.current.style.width = '100%';
            mapContainerRef.current.style.minWidth = '300px'; // Ensure minimum width
            mapContainerRef.current.style.opacity = '1'; // Ensure visibility

            // Force a reflow to ensure dimensions are applied
            const forceReflow = mapContainerRef.current.offsetHeight;

            debugMap.logEvent('RESIZE_CONTAINER_DIMENSIONS', {
              width: mapContainerRef.current.offsetWidth,
              height: mapContainerRef.current.offsetHeight,
              isMobile: isMobileNow,
              crossedBreakpoint
            });
          }

          // If we crossed a breakpoint, handle it differently
          if (crossedBreakpoint) {
            debugMap.logEvent('VIEWPORT_BREAKPOINT_CROSSED', {
              from: wasMobile ? 'mobile' : 'desktop',
              to: isMobileNow ? 'mobile' : 'desktop',
              containerDimensions: mapContainerRef.current ? {
                width: mapContainerRef.current.offsetWidth,
                height: mapContainerRef.current.offsetHeight
              } : null
            });

            // For mobile to desktop transition, we need to be extra careful
            if (!isMobileNow && wasMobile) {
              // When going from mobile to desktop, ensure container is ready first
              resizeTimeoutRef.current = setTimeout(() => {
                if (mountedRef.current && mapContainerRef.current) {
                  // Ensure container is visible with proper dimensions
                  mapContainerRef.current.style.display = 'block';
                  mapContainerRef.current.style.visibility = 'visible';
                  mapContainerRef.current.style.height = '300px';
                  mapContainerRef.current.style.minHeight = '300px';
                  mapContainerRef.current.style.width = '100%';
                  mapContainerRef.current.style.minWidth = '300px';
                  mapContainerRef.current.style.opacity = '1';

                  // Force a reflow
                  const forceReflow = mapContainerRef.current.offsetHeight;

                  debugMap.logEvent('MOBILE_TO_DESKTOP_TRANSITION', {
                    containerDimensions: {
                      width: mapContainerRef.current.offsetWidth,
                      height: mapContainerRef.current.offsetHeight
                    }
                  });

                  // Resize the map to fit the new container dimensions
                  if (mapInstance.current && mountedRef.current) {
                    mapInstance.current.resize();

                    // Force update markers and route after resize with additional checks
                    setTimeout(() => {
                      if (mountedRef.current && mapInstance.current) {
                        // Check if map is in a valid state before updating markers
                        if (mapInstance.current.loaded()) {
                          updateMapMarkers();
                          debugMap.logEvent('MARKERS_UPDATED_AFTER_MOBILE_TO_DESKTOP');
                        } else {
                          debugMap.logEvent('MARKERS_UPDATE_SKIPPED_MAP_NOT_LOADED');
                        }
                      } else {
                        debugMap.logEvent('MARKERS_UPDATE_SKIPPED_AFTER_RESIZE', {
                          mapExists: !!mapInstance.current,
                          mounted: mountedRef.current
                        });
                      }
                    }, 250);
                  }
                }
              }, 300);
            } else {
              // For desktop to mobile, resize after a delay
              resizeTimeoutRef.current = setTimeout(() => {
                if (mountedRef.current && mapContainerRef.current) {
                  // Ensure container is visible with proper dimensions
                  mapContainerRef.current.style.display = 'block';
                  mapContainerRef.current.style.visibility = 'visible';
                  mapContainerRef.current.style.height = '250px';
                  mapContainerRef.current.style.minHeight = '250px';
                  mapContainerRef.current.style.width = '100%';
                  mapContainerRef.current.style.minWidth = '300px';
                  mapContainerRef.current.style.opacity = '1';

                  // Force a reflow
                  const forceReflow = mapContainerRef.current.offsetHeight;

                  debugMap.logEvent('DESKTOP_TO_MOBILE_TRANSITION', {
                    containerDimensions: {
                      width: mapContainerRef.current.offsetWidth,
                      height: mapContainerRef.current.offsetHeight
                    }
                  });

                  if (mapInstance.current) {
                    mapInstance.current.resize();

                    // Force update markers and route after resize with additional checks
                    setTimeout(() => {
                      if (mountedRef.current && mapInstance.current) {
                        // Check if map is in a valid state before updating markers
                        if (mapInstance.current.loaded()) {
                          updateMapMarkers();
                          debugMap.logEvent('MARKERS_UPDATED_AFTER_DESKTOP_TO_MOBILE');
                        } else {
                          debugMap.logEvent('MARKERS_UPDATE_SKIPPED_MAP_NOT_LOADED');
                        }
                      } else {
                        debugMap.logEvent('MARKERS_UPDATE_SKIPPED_AFTER_RESIZE', {
                          mapExists: !!mapInstance.current,
                          mounted: mountedRef.current
                        });
                      }
                    }, 250);
                  }
                }
              }, 250);
            }
          } else {
            // For non-breakpoint changes, just resize the map
            resizeTimeoutRef.current = setTimeout(() => {
              if (mapInstance.current && mountedRef.current) {
                mapInstance.current.resize();

                // Update markers after resize for non-breakpoint changes too
                setTimeout(() => {
                  if (mountedRef.current && mapInstance.current) {
                    // Check if map is in a valid state before updating markers
                    if (mapInstance.current.loaded()) {
                      updateMapMarkers();
                      debugMap.logEvent('MARKERS_UPDATED_AFTER_RESIZE');
                    } else {
                      debugMap.logEvent('MARKERS_UPDATE_SKIPPED_MAP_NOT_LOADED');
                    }
                  } else {
                    debugMap.logEvent('MARKERS_UPDATE_SKIPPED_AFTER_RESIZE', {
                      mapExists: !!mapInstance.current,
                      mounted: mountedRef.current
                    });
                  }
                }, 100);
              }
            }, 100);
          }
        } catch (error) {
          debugMap.logError('RESIZE_ERROR', error);
        }
      }

      // Update the previous width reference
      previousWidthRef.current = currentWidth;
    };

    // Add event listeners
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    // Initial size check
    handleResize();

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, [updateMapMarkers]);

  // Update the waitForDimensions function to be more robust
  const waitForDimensions = () => new Promise<void>((resolve) => {
    let attempts = 0;
    const MAX_ATTEMPTS = 30; // 3 seconds total

    const checkDimensions = () => {
      if (!mountedRef.current) {
        debugMap.logEvent('COMPONENT_UNMOUNTED_DURING_DIMENSION_CHECK');
        // Resolve anyway instead of rejecting
        resolve();
        return;
      }

      if (!mapContainerRef.current) {
        debugMap.logEvent('CONTAINER_LOST_DURING_DIMENSION_CHECK');
        // Resolve anyway instead of rejecting
        resolve();
        return;
      }

      const { offsetWidth, offsetHeight } = mapContainerRef.current;
      debugMap.logEvent('CHECKING_CONTAINER_DIMENSIONS', {
        attempt: attempts,
        dimensions: { offsetWidth, offsetHeight }
      });

      // Make sure container is visible and has dimensions
      if (offsetWidth > 0 && offsetHeight > 0) {
        // Force container to be visible
        mapContainerRef.current.style.display = 'block';
        mapContainerRef.current.style.visibility = 'visible';
        mapContainerRef.current.style.width = '100%';
        mapContainerRef.current.style.minWidth = '300px';
        mapContainerRef.current.style.opacity = '1';

        // Log success and resolve
        debugMap.logEvent('CONTAINER_DIMENSIONS_READY', {
          width: offsetWidth,
          height: offsetHeight,
          attempts
        });
        resolve();
      } else if (attempts >= MAX_ATTEMPTS / 2) {
        // After half the attempts, try a more aggressive approach
        if (mapContainerRef.current) {
          // Force dimensions with explicit values
          mapContainerRef.current.style.display = 'block';
          mapContainerRef.current.style.visibility = 'visible';
          mapContainerRef.current.style.width = '100%';
          mapContainerRef.current.style.minWidth = '300px';
          mapContainerRef.current.style.height = isMobileView ? '250px' : '300px';
          mapContainerRef.current.style.minHeight = isMobileView ? '250px' : '300px';
          mapContainerRef.current.style.opacity = '1';

          // Force a reflow
          const forceReflow = mapContainerRef.current.offsetHeight;

          debugMap.logEvent('FORCING_EXPLICIT_DIMENSIONS', {
            height: mapContainerRef.current.style.height,
            width: mapContainerRef.current.style.width,
            offsetWidth: mapContainerRef.current.offsetWidth,
            offsetHeight: mapContainerRef.current.offsetHeight
          });

          // Check if dimensions are now valid
          if (mapContainerRef.current.offsetWidth > 0 && mapContainerRef.current.offsetHeight > 0) {
            debugMap.logEvent('EXPLICIT_DIMENSIONS_SUCCEEDED');
            resolve();
            return;
          }

          // If we're at max attempts, resolve anyway
          if (attempts >= MAX_ATTEMPTS) {
            debugMap.logEvent('MAX_ATTEMPTS_REACHED_RESOLVING_ANYWAY');
            resolve();
            return;
          }
        }
      } else if (attempts >= MAX_ATTEMPTS) {
        // If we've tried too many times, force dimensions and resolve anyway
        if (mapContainerRef.current) {
          // Force container to be visible with explicit dimensions
          mapContainerRef.current.style.display = 'block';
          mapContainerRef.current.style.visibility = 'visible';
          mapContainerRef.current.style.height = isMobileView ? '250px' : '300px';
          mapContainerRef.current.style.minHeight = isMobileView ? '250px' : '300px';
          mapContainerRef.current.style.width = '100%';
          mapContainerRef.current.style.minWidth = '300px';
          mapContainerRef.current.style.opacity = '1';

          // Force a reflow
          const forceReflow = mapContainerRef.current.offsetHeight;

          debugMap.logEvent('FORCING_CONTAINER_DIMENSIONS', {
            height: mapContainerRef.current.style.height,
            width: mapContainerRef.current.style.width
          });

          // Resolve anyway after forcing dimensions
          resolve();
        } else {
          // Even if container is lost, resolve anyway
          debugMap.logEvent('CONTAINER_LOST_AFTER_MAX_ATTEMPTS');
          resolve();
        }
      } else {
        attempts++;
        setTimeout(checkDimensions, 100);
      }
    };

    checkDimensions();
  });

  // Initialize map on mount or when key changes (forced re-mount)
  useEffect(() => {
    debugMap.logEvent('INIT_EFFECT_TRIGGERED', {
      mapKey,
      loadAttempts,
      hasContainer: !!mapContainerRef.current
    });

    if (!mapContainerRef.current) {
      debugMap.logError('CONTAINER_NOT_FOUND', 'Map container ref is null');
      throw new Error('Map container not found');
    }

    mountedRef.current = true;
    let retryCount = 0;
    const MAX_RETRIES = 3;

    // Set loading state initially
    setIsStyleLoading(true);

    const initMap = async () => {
      debugMap.logEvent('INIT_MAP_FUNCTION_CALLED', {
        retryCount,
        containerDimensions: mapContainerRef.current ? {
          clientWidth: mapContainerRef.current.clientWidth,
          clientHeight: mapContainerRef.current.clientHeight,
          offsetWidth: mapContainerRef.current.offsetWidth,
          offsetHeight: mapContainerRef.current.offsetHeight
        } : 'null'
      });

      try {
        // Ensure container is visible and has proper dimensions
        if (mapContainerRef.current) {
          // Force container to be visible with explicit dimensions
          mapContainerRef.current.style.display = 'block';
          mapContainerRef.current.style.visibility = 'visible';
          mapContainerRef.current.style.height = isMobileView ? '250px' : '300px';

          // Force a reflow to ensure dimensions are applied
          const forceReflow = mapContainerRef.current.offsetHeight;
        }

        // Wait for dimensions before proceeding
        await waitForDimensions();
        debugMap.logEvent('CONTAINER_DIMENSIONS_READY');

        // Original initialization code continues...
        await new Promise(resolve => setTimeout(resolve, 50));
        debugMap.logEvent('PRE_INIT_DELAY_COMPLETE');

        if (!mountedRef.current) {
          debugMap.logEvent('INIT_ABORTED_UNMOUNTED');
          return;
        }

        if (!mapContainerRef.current) {
          debugMap.logError('CONTAINER_LOST_DURING_INIT', 'Container ref became null during initialization');
          return;
        }

        // Log container dimensions before map init
        const containerDimensions = {
          clientWidth: mapContainerRef.current.clientWidth,
          clientHeight: mapContainerRef.current.clientHeight,
          offsetWidth: mapContainerRef.current.offsetWidth,
          offsetHeight: mapContainerRef.current.offsetHeight,
          style: {
            width: mapContainerRef.current.style.width || 'not set',
            height: mapContainerRef.current.style.height || 'not set'
          }
        };

        debugMap.logEvent('CONTAINER_DIMENSIONS_BEFORE_INIT', containerDimensions);

        // Force a layout recalculation
        const forceReflow = mapContainerRef.current.offsetHeight;
        debugMap.logEvent('FORCED_REFLOW', { value: forceReflow });

        // Clear any existing map first
        mapInstance.destroy();
        mapInstance.styleLoadRetries = 0;

        // Initialize the map with error handling for style loading
        debugMap.logEvent('MAP_INIT_STARTING');

        // Create a minimal fallback style in case the main style fails to load
        const fallbackStyle = {
          version: 8,
          sources: {},
          layers: []
        };

        // Try to get the style, but use fallback if it fails
        let mapStyle;
        try {
          mapStyle = getMapStyle();
          debugMap.logEvent('MAP_STYLE_RETRIEVED', { style: mapStyle });
        } catch (styleError) {
          debugMap.logError('MAP_STYLE_ERROR', styleError);
          mapStyle = 'mapbox://styles/mapbox/light-v11'; // Safe fallback
        }

        // Create map with error handling for network requests
        const map = mapInstance.init(mapContainerRef.current, () => {
          if (mountedRef.current) {
            debugMap.logEvent('ON_STYLE_LOAD_CALLBACK');
            updateMapMarkers();
            setIsStyleLoading(false);
          } else {
            debugMap.logEvent('ON_STYLE_LOAD_CALLBACK_IGNORED', { reason: 'component unmounted' });
          }
        });

        if (!map) {
          throw new Error('Failed to initialize map');
        }

        // Add a special error handler for the specific error we're seeing
        map.on('error', (e) => {
          // Check if this is the "Cannot read properties of undefined (reading 'send')" error
          if (e.error && e.error.message && e.error.message.includes("Cannot read properties of undefined")) {
            debugMap.logEvent('MAPBOX_NETWORK_ERROR_INTERCEPTED', {
              message: e.error.message,
              source: e.source
            });
            // This error is related to network requests, we can safely ignore it
            return;
          }

          // Log other errors normally
          debugMap.logError('MAP_INSTANCE_ERROR', e);
        });

        debugMap.logEvent('MAP_INIT_SUCCEEDED');

        // Wait for style to load with retry capability
        try {
          debugMap.logEvent('WAITING_FOR_STYLE');
          // Try to wait for style, but don't let it block the UI
          try {
            await mapInstance.waitForStyle(3000); // Reduced timeout to fail faster
            debugMap.logEvent('STYLE_LOAD_SUCCEEDED');
          } catch (styleError) {
            // Log the error but continue anyway
            debugMap.logError('STYLE_LOAD_WARNING', styleError);
            debugMap.logEvent('CONTINUING_DESPITE_STYLE_WARNING');
          }

          if (mountedRef.current) {
            updateMapMarkers();
            setIsStyleLoading(false);
            debugMap.logEvent('MARKERS_UPDATED');
          } else {
            debugMap.logEvent('STYLE_LOADED_COMPONENT_UNMOUNTED');
          }
        } catch (styleError) {
          debugMap.logError('STYLE_LOAD_FAILED', styleError);

          // Even if style loading fails, we'll continue with the map
          if (mountedRef.current) {
            debugMap.logEvent('CONTINUING_DESPITE_STYLE_FAILURE');
            updateMapMarkers();
            setIsStyleLoading(false);
          }
        }

        // Handle theme changes
        debugMap.logEvent('SETTING_UP_THEME_OBSERVER');
        const handleMapThemeChange = (event: any) => {
          try {
            console.log('MapComponent: Map theme change event received', event.detail);
            if (map && map.loaded()) {
              const newStyle = event.detail.style || getMapStyle();
              console.log('MapComponent: Changing map style to:', newStyle);
              map.setStyle(newStyle);
            }
          } catch (error) {
            console.error('MapComponent: Error handling map theme change:', error);
          }
        };
        
        window.addEventListener('mapthemechange', handleMapThemeChange);
        
        // Clean up event listener on component unmount
        return () => {
          window.removeEventListener('mapthemechange', handleMapThemeChange);
        };
      } catch (error) {
        debugMap.logError('INIT_ERROR', error);

        if (mountedRef.current) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          setMapInitError(errorMessage);
          setIsStyleLoading(false);

          // Retry initialization on failure
          if (retryCount < MAX_RETRIES) {
            retryCount++;
            debugMap.logEvent('INIT_RETRY_SCHEDULED', {
              retryCount,
              maxRetries: MAX_RETRIES,
              delayMs: 1000 * retryCount
            });

            // Wait before retrying
            setTimeout(() => {
              if (mountedRef.current) {
                debugMap.logEvent('EXECUTING_RETRY', { retryCount });
                initMap();
              } else {
                debugMap.logEvent('RETRY_ABORTED', { reason: 'component unmounted' });
              }
            }, 1000 * retryCount);
          } else {
            debugMap.logEvent('ERROR_IGNORED', { reason: 'component unmounted' });
          }
        }
      }
    };

    initMap();

    // Cleanup function
    return () => {
      debugMap.logEvent('INIT_EFFECT_CLEANUP');
      mountedRef.current = false;
      cleanUpMap();
    };
  }, [mapKey, cleanUpMap, loadAttempts, isMobileView]);

  // Update the map when form type changes
  useEffect(() => {
    if (mapInstance.current && mountedRef.current) {
      debugMap.logEvent('FORM_DATA_CHANGED', { formType });
      updateMapMarkers();
    }
  }, [formType, pointToPointData, hourlyData, airportData]);

  // In case we have repeated failures, add an automatic reinitialize after a certain number of attempts
  useEffect(() => {
    // If we've tried more than 3 times and still have an error, try a more aggressive approach
    if (loadAttempts > 3 && mapInitError) {
      debugMap.logEvent('AUTO_RECOVERY_TRIGGERED', {
        loadAttempts,
        error: mapInitError
      });

      // Schedule a delayed retry with cleaned up DOM
      const recoveryTimeout = setTimeout(() => {
        if (mountedRef.current) {
          debugMap.logEvent('EXECUTING_AUTO_RECOVERY');
          // Force a complete remount
          cleanUpMap();
          setMapInitError(null);
          setIsStyleLoading(true);
          setMapKey(Date.now());
        }
      }, 2000);

      return () => clearTimeout(recoveryTimeout);
    }
  }, [loadAttempts, mapInitError, cleanUpMap]);

  return (
    <div className="map-container relative"
      style={{
        minHeight: isMobileView ? '250px' : '300px',
        height: isMobileView ? '250px' : '300px',
        background: 'rgba(0,0,0,0.05)',
        border: '1px solid rgba(0,0,0,0.1)',
        overflow: 'hidden',
        position: 'relative',
        transition: 'all 0.3s ease-out',
        display: 'block',
        visibility: 'visible',
        width: '100%',
        minWidth: '300px',
        opacity: '1'
      }}>
      <div
        key={mapKey}
        ref={mapContainerRef}
        className="absolute inset-0 w-full h-full bg-background"
        style={{
          minHeight: isMobileView ? '250px' : '300px',
          height: '100%',
          position: 'absolute',
          overflow: 'hidden',
          opacity: '1',
          visibility: 'visible',
          display: 'block',
          left: 0,
          top: 0,
          right: 0,
          bottom: 0,
          transition: 'all 0.3s ease-out',
          width: '100%',
          minWidth: '300px'
        }}
        data-is-mobile={isMobileView.toString()}
        data-map-key={mapKey}
        data-viewport-width={viewportWidth}
      />

      {/* Debug overlay - Only visible in development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-2 left-2 z-20 bg-background/80 text-xs p-1 rounded opacity-60 hover:opacity-100 pointer-events-none">
          <div>{isMobileView ? '📱 Mobile' : '🖥️ Desktop'}</div>
          <div>Key: {mapKey.toString().slice(-4)}</div>
          <div>W: {viewportWidth}px</div>
        </div>
      )}

      {isStyleLoading && (
        <div className="absolute inset-0 bg-background/50 flex items-center justify-center z-10">
          <div className="text-sm text-disabled flex flex-col items-center">
            <svg className="animate-spin h-8 w-8 text-primary mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Loading map...</span>
          </div>
        </div>
      )}

      {mapInitError && (
        <div className="absolute inset-0 bg-background/90 flex items-center justify-center z-10">
          <div className="text-sm text-error text-center p-4 max-w-md">
            <p className="font-medium">Error loading map</p>
          </div>
        </div>
      )}
    </div>
  );
});
