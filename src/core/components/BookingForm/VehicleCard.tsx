import React from 'react';
import { cn } from '@core/lib/utils';

interface VehicleCardProps {
    type: string;
    imageUrl: string;
    selected: boolean;
    onSelect: () => void;
}

export const VehicleCard: React.FC<VehicleCardProps> = ({
    type,
    imageUrl,
    selected,
    onSelect
}) => {
    const vehicleInfo = {
        'sedan': { name: 'Sedan', capacity: 3, description: 'Leather seats, Tinted privacy glass, Premium sound system' },
        'luxury-sedan': { name: 'Luxury Sedan', capacity: 3, description: 'Plush leather seating, Rear climate control, Ambient lighting' },
        'suv': { name: 'SUV', capacity: 6, description: 'Spacious interior, Leather seats, All-wheel drive, Premium sound system' },
        'luxury-suv': { name: 'Luxury SUV', capacity: 6, description: 'Leather seating with extra legroom, Advanced climate control, On-board WiFi' },
        'sprinter': { name: 'Mercedes Sprinter', capacity: 12, description: 'Spacious interior, Premium sound system, On-board WiFi, USB charging ports' },
        'stretch': { name: 'Stretch Limousine', capacity: 10, description: 'Leather seating, Mood lighting, Mini bar, Premium surround sound system' },
        'hummer': { name: 'Hummer Limousine', capacity: 14, description: 'Leather seating, LED lighting, Mini bar, State-of-the-art sound system' },
        'party-bus': { name: 'Party Bus', capacity: 20, description: 'Dance floor, LED lighting, High-end sound system, Bar area' },
        'mini-bus': { name: 'Mini Bus', capacity: 24, description: 'Comfortable seating, Ample legroom, Air conditioning, Generous luggage space' },
        'coach-bus': { name: 'Coach Bus', capacity: 50, description: 'Reclining seats, Climate control, On-board restroom, Entertainment system' }
    }[type] || { name: type, capacity: 0, description: '' };

    return (
        <button
            onClick={onSelect}
            className={cn(
                "w-full text-left p-6 rounded-lg transition-all duration-200",
                selected
                    ? "bg-primary text-white border-none"
                    : "bg-surface hover:bg-surface-light border border-border"
            )}
        >
            {/* Vehicle Image */}
            <div className="relative aspect-video bg-surface-dark rounded-lg mb-4 flex items-center justify-center overflow-hidden">
                <div className="w-full h-full max-w-[300px] mx-auto">
                    <img
                        src={imageUrl}
                        alt={vehicleInfo.name}
                        className="w-full h-full object-contain"
                        onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/wp-content/uploads/limo-assets/vehicles/default-vehicle.png';
                        }}
                    />
                </div>
                {/* Passenger Capacity Badge */}
                <div className={cn(
                    "absolute top-2 right-2 flex items-center gap-1 px-2 py-1 rounded-lg text-sm",
                    selected ? "bg-primary text-white" : "bg-surface-dark text-primary"
                )}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                        <path d="M4.5 6.375a4.125 4.125 0 118.25 0 4.125 4.125 0 01-8.25 0zM14.25 8.625a3.375 3.375 0 116.75 0 3.375 3.375 0 01-6.75 0zM1.5 19.125a7.125 7.125 0 0114.25 0v.003l-.001.119a.75.75 0 01-.363.63 13.067 13.067 0 01-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 01-.364-.63l-.001-.122zM17.25 19.128l-.001.144a2.25 2.25 0 01-.233.96 10.088 10.088 0 005.06-********** 0 00.42-.643 4.875 4.875 0 00-6.957-4.611 8.586 8.586 0 011.71 5.157v.003z" />
                    </svg>
                    <span>{vehicleInfo.capacity}</span>
                </div>
            </div>

            {/* Vehicle Details */}
            <h3 className="text-lg font-medium mb-2">{vehicleInfo.name}</h3>
            <p className={cn(
                "text-sm",
                selected ? "text-white/80" : "text-theme-muted"
            )}>{vehicleInfo.description}</p>
        </button>
    );
}; 