import { BookingType } from '@core/types/booking';

export interface BookingFormProps {
  apiKeys: {
    here: string;
    mapbox: string;
    resend: string;
    salesmate: string;
  };
  nonce: string;
  ajaxUrl: string;
  vehicleImages: Record<string, string>;
  formType?: BookingType;
  hideThemeSelector?: boolean;
  inheritThemeColors?: boolean;
}

export interface Vehicle {
  id: string;
  name: string;
  description: string;
  price?: number;
  image: string;
  capacity: number;
}

export interface BookingData {
  pickupLocation: string;
  dropoffLocation: string;
  pickupDate: string;
  pickupTime: string;
  vehicleId: string;
  name: string;
  email: string;
  phone: string;
  specialInstructions?: string;
}

export interface Location {
  address: string;
  coordinates: [number, number];
}