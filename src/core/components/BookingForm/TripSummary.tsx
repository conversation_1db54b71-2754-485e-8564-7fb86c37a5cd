import { useEffect, useRef, useState } from 'react';
import { useBookingStore } from '@core/store/bookingStore';
import { cn } from '@core/lib/utils';
import { format } from 'date-fns';
import {
  XMarkIcon,
  MapPinIcon,
  CalendarIcon,
  ClockIcon,
  UserGroupIcon,
  UserIcon,
  ChevronRightIcon,
  BuildingOfficeIcon,
  PaperAirplaneIcon,
  ArrowPathRoundedSquareIcon,
  ArrowLongRightIcon,
  CheckBadgeIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { ErrorBoundary } from '@core/components/ErrorBoundary';
import { MapComponent } from './MapComponent';
import { CollapsibleSection } from './CollapsibleSection';
import { LocationDisplay } from './LocationDisplay';

// Vehicle data
const vehicles = [
  {
    id: 'sedan',
    name: 'Sedan',
    description: 'Leather seats, Tinted privacy glass, Premium sound system, Smooth and quiet ride',
    image: '/vehicles/sedan.png',
    capacity: 3
  },
  {
    id: 'luxury-sedan',
    name: 'Luxury Sedan',
    description: 'Plush leather seating, Rear climate control, Ambient lighting',
    image: '/vehicles/luxury-sedan.png',
    capacity: 3
  },
  {
    id: 'suv',
    name: 'SUV',
    description: 'Spacious interior, Leather seats, All-wheel drive, Premium sound system, Ample luggage space',
    image: '/vehicles/suv.png',
    capacity: 6
  },
  {
    id: 'luxury-suv',
    name: 'Luxury SUV',
    description: 'Leather seating with extra legroom, Advanced climate control, On-board WiFi, Ambient lighting',
    image: '/vehicles/luxury-suv.png',
    capacity: 6
  },
  {
    id: 'sprinter',
    name: 'Mercedes Sprinter',
    description: 'Spacious interior, Premium sound system, On-board WiFi, USB charging ports',
    image: '/vehicles/sprinter.png',
    capacity: 12
  },
  {
    id: 'stretch',
    name: 'Stretch Limousine',
    description: 'Leather seating, Mood lighting, Mini bar, Premium surround sound system, Privacy partition',
    image: '/vehicles/stretch.png',
    capacity: 10
  },
  {
    id: 'hummer',
    name: 'Hummer Limousine',
    description: 'Leather seating, LED lighting, Mini bar, State-of-the-art sound system, Privacy partition, Touch screen controls, Extra spacious interior',
    image: '/vehicles/hummer.png',
    capacity: 14
  },
  {
    id: 'party-bus',
    name: 'Party Bus',
    description: 'Dance floor, LED lighting, High-end sound system, Bar area, Comfortable lounge seating, TV screens',
    image: '/vehicles/party-bus.jpg',
    capacity: 20
  },
  {
    id: 'mini-bus',
    name: 'Mini Bus',
    description: 'Comfortable seating, Ample legroom, Air conditioning, Generous luggage space, On-board WiFi',
    image: '/vehicles/mini-bus.png',
    capacity: 24
  },
  {
    id: 'coach-bus',
    name: 'Coach Bus',
    description: 'Reclining seats, Climate control, On-board restroom, Entertainment system with multiple screens, Ample luggage space, WiFi connectivity',
    image: '/vehicles/coach-bus.png',
    capacity: 50
  }
];

interface TripSummaryProps {
  inPanel?: boolean;
}

const TimeDisplay = ({ time }: { time: string }) => {
  if (!time) return null;

  const [timeStr, period] = time.split(' ');
  const [hours, minutes] = timeStr.split(':');

  return (
    <div className="flex items-center gap-2 bg-surface rounded-lg p-2">
      <div className="flex items-center gap-1">
        <div className="bg-primary/20 rounded px-2 py-1 text-theme-primary font-medium">
          {hours}
        </div>
        <span className="text-theme-primary font-bold">:</span>
        <div className="bg-primary/20 rounded px-2 py-1 text-theme-primary font-medium">
          {minutes}
        </div>
        <div className="bg-primary/20 rounded px-2 py-1 text-theme-primary text-sm font-medium">
          {period}
        </div>
      </div>
      <ClockIcon className="w-4 h-4 text-theme-primary" />
    </div>
  );
};

export const TripSummary = ({ inPanel = false }: TripSummaryProps) => {
  const {
    formType,
    pointToPointData,
    hourlyData,
    airportData,
    currentStep
  } = useBookingStore();

  const [isUpdating, setIsUpdating] = useState(false);
  const previousDataRef = useRef<string>("");

  // Add new state for mobile section management
  const [openSection, setOpenSection] = useState<string>('map');
  const tripSummaryRef = useRef<HTMLDivElement>(null);

  console.log('TripSummary rendered, isUpdating:', isUpdating);

  // Function to close the trip summary
  const closeTripSummary = () => {
    console.log('closeTripSummary called from TripSummary component');
    if (window.closeTripSummary) {
      window.closeTripSummary();
    } else {
      console.log('No window.closeTripSummary found, using history.back()');
      window.history.back();
    }
  };

  useEffect(() => {
    const currentData = JSON.stringify({ formType, pointToPointData, hourlyData, airportData });

    if (previousDataRef.current && previousDataRef.current !== currentData) {
      setIsUpdating(true);

      const timer = setTimeout(() => {
        setIsUpdating(false);
      }, 2000);

      return () => clearTimeout(timer);
    }

    previousDataRef.current = currentData;
  }, [formType, pointToPointData, hourlyData, airportData]);

  // Handle click outside for mobile view
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Only proceed if we're in mobile view and not in a panel
      if (!inPanel && window.innerWidth <= 768) {
        // Check if the click was inside the trip summary ref
        if (tripSummaryRef.current && !tripSummaryRef.current.contains(event.target as Node)) {
          // Close the trip summary only if the click was outside
          console.log('Click outside detected, closing trip summary');
          closeTripSummary();
        } else {
          // Click was inside the trip summary, prevent closing
          console.log('Click inside detected, keeping trip summary open');
          event.stopPropagation();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [inPanel]);

  // Handle section toggling
  const handleSectionToggle = (sectionId: string) => {
    if (sectionId === openSection) {
      setOpenSection('');
    } else {
      setOpenSection(sectionId);
      if (sectionId !== 'map') {
        // Collapse map when opening other sections
        setOpenSection(sectionId);
      }
    }
  };

  const getActiveData = () => {
    switch (formType) {
      case 'point-to-point':
        return pointToPointData;
      case 'hourly':
        return hourlyData;
      case 'airport':
        return airportData;
      default:
        return pointToPointData;
    }
  };

  const formatAddress = (address: string, isStop: boolean = false) => {
    if (!address) return null;
    const parts = address.split(',');

    if (isStop) {
      return (
        <span className="text-sm text-text-primary opacity-70">
          {parts[0].trim()}
        </span>
      );
    }

    return parts.map((part, i) => (
      <span key={i} className={i === 0 ? "font-medium text-text-primary" : "text-text-primary opacity-70 text-sm"}>
        {part.trim()}
        {i < parts.length - 1 && ','}<br />
      </span>
    ));
  };

  const bookingData = getActiveData();
  const dateObj = formType === 'airport'
    ? airportData.departureDate ? new Date(airportData.departureDate) : null
    : bookingData.pickupDate ? new Date(bookingData.pickupDate) : null;

  const returnDateObj = formType === 'airport' && airportData.isRoundTrip
    ? airportData.returnDate ? new Date(airportData.returnDate) : null
    : formType === 'point-to-point' && pointToPointData.isRoundTrip
      ? pointToPointData.returnDate ? new Date(pointToPointData.returnDate) : null
      : null;

  const formattedDate = dateObj ? format(dateObj, 'EEE, MMM d, yyyy') : 'Date not selected';
  const formattedTime = dateObj ? format(dateObj, 'h:mm a') : 'Time not selected';
  const formattedReturnDate = returnDateObj ? format(returnDateObj, 'EEE, MMM d, yyyy') : 'Date not selected';
  const formattedReturnTime = returnDateObj ? format(returnDateObj, 'h:mm a') : 'Time not selected';

  const getDropoffLocation = () => {
    return bookingData?.dropoffLocation?.address || '';
  };

  // Mobile Trip Summary Display
  const renderMobileTripSummary = () => (
    <div 
      className="block lg:hidden space-y-6" 
      ref={tripSummaryRef}
      onClick={(e) => {
        // Stop propagation to prevent the backdrop click handler from firing
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
        console.log('Trip summary container clicked, preventing propagation');
      }}
    >
      {/* Map Container with Service Type Overlay */}
      <CollapsibleSection
        title="Trip Route"
        sectionId="map"
        isOpen={openSection === 'map'}
        onToggle={handleSectionToggle}
        isUpdating={isUpdating}
        titleClassName="text-text-primary"
      >
        <div className="relative w-full h-[300px] rounded-lg overflow-hidden">
          {formType !== 'multi-day' && (
            <div className="absolute inset-0 bg-surface rounded-lg">
              <MapComponent key={formType} />
            </div>
          )}

          {/* Service Type Indicators */}
          <div className="absolute top-3 right-3 flex gap-2 text-xs">
            <div className="flex items-center gap-1 px-2 py-1 bg-primary rounded">
              {formType === 'point-to-point' && (
                <>
                  <ArrowLongRightIcon className="w-3.5 h-3.5 text-white" />
                  <span className="text-white">Point To Point</span>
                </>
              )}
              {formType === 'hourly' && (
                <>
                  <ClockIcon className="w-3.5 h-3.5 text-white" />
                  <span className="text-white">By the Hour</span>
                </>
              )}
              {formType === 'airport' && (
                <>
                  <PaperAirplaneIcon className="w-3.5 h-3.5 text-white" />
                  <span className="text-white">Airport Transfer</span>
                </>
              )}
            </div>
            {formType === 'airport' && airportData.isRoundTrip && (
              <div className="flex items-center gap-1 px-2 py-1 bg-primary rounded">
                <ArrowPathRoundedSquareIcon className="w-3.5 h-3.5 text-white" />
                <span className="text-white">Round Trip</span>
              </div>
            )}
          </div>
        </div>
      </CollapsibleSection>

      {/* Trip Details Section */}
      <CollapsibleSection
        title="Trip Details"
        sectionId="details"
        isOpen={openSection === 'details'}
        onToggle={handleSectionToggle}
        isUpdating={isUpdating}
      >
        <div className="space-y-6">
          {/* Date and Time Section */}
          <div className="grid grid-cols-2 gap-4">
            {/* Pickup Date */}
            <div>
              <h4 className="text-sm text-theme-muted mb-2">Date/Time</h4>
              <div className="space-y-2">
                <div className="bg-primary rounded-lg p-3 text-center min-w-[60px]">
                  <div className="text-sm text-white uppercase">{formattedDate}</div>
                  <div className="text-xl font-bold text-white">{formattedTime}</div>
                </div>
                {formType === 'airport' && (
                  <div className="text-text-primary text-sm flex items-center justify-center bg-surface/50 rounded-lg p-2">
                    <PaperAirplaneIcon className="w-4 h-4 mr-1" />
                    <span>Flight: {airportData.departureFlight || 'Not specified'}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Return Trip Date */}
            {((formType === 'airport' && airportData.isRoundTrip) ||
              (formType === 'point-to-point' && pointToPointData.isRoundTrip)) && (
                <div>
                  <h4 className="text-sm text-theme-muted mb-2">Return Date/Time</h4>
                  <div className="space-y-2">
                    <div className="bg-primary rounded-lg p-3 text-center min-w-[60px]">
                      <div className="text-sm text-white uppercase">{formattedReturnDate}</div>
                      <div className="text-xl font-bold text-white">{formattedReturnTime}</div>
                    </div>
                    {formType === 'airport' && (
                      <div className="text-text-primary text-sm flex items-center justify-center bg-surface/50 rounded-lg p-2">
                        <PaperAirplaneIcon className="w-4 h-4 mr-1" />
                        <span>Flight: {airportData.returnFlight || 'Not specified'}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
          </div>

          {/* Timeline Section */}
          <div className="relative pl-6 space-y-4">
            {/* Vertical line */}
            <div className="absolute left-[11px] top-0 bottom-0 w-[2px] bg-primary z-0" style={{ height: 'calc(100% + 1rem)', marginTop: '-0.5rem' }} />

            {/* Pickup Location */}
            <div className="relative z-10">
              <div className="absolute left-[-18px] w-4 h-4 rounded-full border border-primary bg-surface flex items-center justify-center z-20">
                <div className="w-1.5 h-1.5 rounded-full bg-primary" />
              </div>
              <div className="bg-surface rounded-lg p-4">
                <div className="text-sm text-text-primary mb-2">Pickup Location</div>
                <div className="space-y-0.5">
                  {formatAddress(bookingData.pickupLocation?.address || '')}
                </div>
              </div>
            </div>

            {/* Stops */}
            {formType === 'point-to-point' && (bookingData as any).stops && (bookingData as any).stops.length > 0 && (
              <div className="relative">
                <div className="bg-surface rounded-lg p-4 mb-4">
                  <div className="text-sm text-text-primary mb-2 flex items-center justify-between">
                    <span>Stops</span>
                    <span className="text-xs text-text-primary opacity-50">{(bookingData as any).stops.length} stop{(bookingData as any).stops.length !== 1 ? 's' : ''}</span>
                  </div>
                  <div className="space-y-2">
                    {(bookingData as any).stops.map((stop: any, index: number) => (
                      <div key={stop.id || index} className="flex items-center gap-2">
                        <div className="flex-shrink-0 w-5 h-5 rounded-full border border-primary/60 bg-surface flex items-center justify-center">
                          <span className="text-xs text-text-primary">{index + 1}</span>
                        </div>
                        <div className="flex-grow">
                          {formatAddress(stop.location?.address || '', true)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Dropoff Location */}
            <div className="relative">
              <div className="absolute left-[-18px] w-4 h-4 rounded-full border border-primary bg-surface flex items-center justify-center">
                <div className="w-1.5 h-1.5 rounded-full bg-primary" />
              </div>
              <div className="bg-surface rounded-lg p-4">
                <div className="text-sm text-text-primary mb-2">Drop-off Location</div>
                <div className="space-y-0.5">
                  {formatAddress(getDropoffLocation())}
                </div>
              </div>
            </div>
          </div>
        </div>
      </CollapsibleSection>

      {/* Passenger Details Section */}
      <CollapsibleSection
        title="Passenger Details"
        sectionId="passenger"
        isOpen={openSection === 'passenger'}
        onToggle={handleSectionToggle}
        isUpdating={isUpdating}
      >
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-surface rounded-lg p-4">
            <div className="flex items-center gap-2 text-sm text-text-primary mb-3">
              <UserGroupIcon className="w-4 h-4" />
              Passengers
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between text-text-primary">
                <span>Adults</span>
                <span>{bookingData.adults}</span>
              </div>
              {bookingData.children > 0 && (
                <div className="flex justify-between text-text-primary">
                  <span>Children</span>
                  <span>{bookingData.children}</span>
                </div>
              )}
            </div>
          </div>

          {/* Car Seats Section */}
          {bookingData.needCarSeats && (
            <div className="bg-surface rounded-lg p-4">
              <div className="flex items-center gap-2 text-sm text-text-primary mb-3">
                <CheckBadgeIcon className="w-4 h-4" />
                Car Seats
              </div>
              <div className="space-y-2 text-sm">
                {bookingData.infantSeats > 0 && (
                  <div className="flex justify-between text-text-primary">
                    <span>Infant (0-1 year)</span>
                    <span>{bookingData.infantSeats}</span>
                  </div>
                )}
                {bookingData.toddlerSeats > 0 && (
                  <div className="flex justify-between text-text-primary">
                    <span>Toddler (1-3 years)</span>
                    <span>{bookingData.toddlerSeats}</span>
                  </div>
                )}
                {bookingData.boosterSeats > 0 && (
                  <div className="flex justify-between text-text-primary">
                    <span>Booster (4-7 years)</span>
                    <span>{bookingData.boosterSeats}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </CollapsibleSection>

      {/* Spacer to ensure content isn't hidden behind the fixed button */}
      <div className="h-24"></div>

      {/* Close button for mobile view - Now fixed at the bottom */}
      <div className="fixed bottom-0 left-0 right-0 p-4 bg-background border-t border-border shadow-lg z-50">
        <button
          onClick={(e) => {
            e.stopPropagation();
            e.nativeEvent.stopImmediatePropagation();
            closeTripSummary();
          }}
          className="w-full py-4 px-4 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors duration-200 flex items-center justify-center gap-2 font-medium"
        >
          <XMarkIcon className="w-5 h-5" />
          <span>Close Trip Summary</span>
        </button>
      </div>
    </div>
  );

  // Desktop Trip Summary Display
  const renderDesktopTripSummary = () => (
    <div className="hidden lg:block space-y-6">
      {/* Map Container with Service Type Overlay */}
      <div>
        <h3 className="text-lg font-light text-text-primary mb-4">Trip Route</h3>
        <div className="relative w-full h-[300px] rounded-lg overflow-hidden">
          {formType !== 'multi-day' && (
            <div className="absolute inset-0 bg-surface rounded-lg">
              <MapComponent key={formType} />
            </div>
          )}

          {/* Service Type Indicators */}
          <div className="absolute top-3 right-3 flex gap-2 text-xs">
            <div className="flex items-center gap-1 px-2 py-1 bg-primary rounded">
              {formType === 'point-to-point' && (
                <>
                  <ArrowLongRightIcon className="w-3.5 h-3.5 text-white" />
                  <span className="text-white">Point To Point</span>
                </>
              )}
              {formType === 'hourly' && (
                <>
                  <ClockIcon className="w-3.5 h-3.5 text-white" />
                  <span className="text-white">By the Hour</span>
                </>
              )}
              {formType === 'airport' && (
                <>
                  <PaperAirplaneIcon className="w-3.5 h-3.5 text-white" />
                  <span className="text-white">Airport Transfer</span>
                </>
              )}
            </div>
            {formType === 'airport' && airportData.isRoundTrip && (
              <div className="flex items-center gap-1 px-2 py-1 bg-primary rounded">
                <ArrowPathRoundedSquareIcon className="w-3.5 h-3.5 text-white" />
                <span className="text-white">Return Trip</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Trip Details Section */}
      <div>
        <h3 className="text-lg font-light text-text-primary mb-4">Trip Details</h3>
        <div className="space-y-6">
          {/* Date and Time Section */}
          <div className="grid grid-cols-2 gap-4">
            {/* Pickup Date */}
            <div>
              <h4 className="text-sm text-theme-muted mb-2">Date/Time</h4>
              <div className="space-y-2">
                <div className="bg-primary rounded-lg p-3 text-center min-w-[60px]">
                  <div className="text-sm text-white uppercase">{formattedDate}</div>
                  <div className="text-xl font-bold text-white">{formattedTime}</div>
                </div>
                {formType === 'airport' && (
                  <div className="text-text-primary text-sm flex items-center justify-center bg-surface/50 rounded-lg p-2">
                    <PaperAirplaneIcon className="w-4 h-4 mr-1" />
                    <span>Flight: {airportData.departureFlight || 'Not specified'}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Return Trip Date */}
            {((formType === 'airport' && airportData.isRoundTrip) ||
              (formType === 'point-to-point' && pointToPointData.isRoundTrip)) && (
                <div>
                  <h4 className="text-sm text-theme-muted mb-2">Return Date/Time</h4>
                  <div className="space-y-2">
                    <div className="bg-primary rounded-lg p-3 text-center min-w-[60px]">
                      <div className="text-sm text-white uppercase">{formattedReturnDate}</div>
                      <div className="text-xl font-bold text-white">{formattedReturnTime}</div>
                    </div>
                    {formType === 'airport' && (
                      <div className="text-text-primary text-sm flex items-center justify-center bg-surface/50 rounded-lg p-2">
                        <PaperAirplaneIcon className="w-4 h-4 mr-1" />
                        <span>Flight: {airportData.returnFlight || 'Not specified'}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
          </div>

          {/* Timeline Section */}
          <div className="relative pl-6 space-y-4">
            {/* Vertical line */}
            <div className="absolute left-[11px] top-0 bottom-0 w-[2px] bg-primary z-0" style={{ height: 'calc(100% + 1rem)', marginTop: '-0.5rem' }} />

            {/* Pickup Location */}
            <div className="relative z-10">
              <div className="absolute left-[-18px] w-4 h-4 rounded-full border border-primary bg-surface flex items-center justify-center z-20">
                <div className="w-1.5 h-1.5 rounded-full bg-primary" />
              </div>
              <div className="bg-surface rounded-lg p-4">
                <div className="text-sm text-text-primary mb-2">Pickup Location</div>
                <div className="space-y-0.5">
                  {formatAddress(bookingData.pickupLocation?.address || '')}
                </div>
              </div>
            </div>

            {/* Stops */}
            {formType === 'point-to-point' && (bookingData as any).stops && (bookingData as any).stops.length > 0 && (
              <div className="relative">
                <div className="bg-surface rounded-lg p-4 mb-4">
                  <div className="text-sm text-text-primary mb-2 flex items-center justify-between">
                    <span>Stops</span>
                    <span className="text-xs text-text-primary opacity-50">{(bookingData as any).stops.length} stop{(bookingData as any).stops.length !== 1 ? 's' : ''}</span>
                  </div>
                  <div className="space-y-2">
                    {(bookingData as any).stops.map((stop: any, index: number) => (
                      <div key={stop.id || index} className="flex items-center gap-2">
                        <div className="flex-shrink-0 w-5 h-5 rounded-full border border-primary/60 bg-surface flex items-center justify-center">
                          <span className="text-xs text-text-primary">{index + 1}</span>
                        </div>
                        <div className="flex-grow">
                          {formatAddress(stop.location?.address || '', true)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Dropoff Location */}
            <div className="relative">
              <div className="absolute left-[-18px] w-4 h-4 rounded-full border border-primary bg-surface flex items-center justify-center">
                <div className="w-1.5 h-1.5 rounded-full bg-primary" />
              </div>
              <div className="bg-surface rounded-lg p-4">
                <div className="text-sm text-text-primary mb-2">Drop-off Location</div>
                <div className="space-y-0.5">
                  {formatAddress(getDropoffLocation())}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Passenger Details Section */}
      <div>
        <h3 className="text-lg font-light text-text-primary mb-4">Passenger Details</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-surface rounded-lg p-4">
            <div className="flex items-center gap-2 text-sm text-text-primary mb-3">
              <UserGroupIcon className="w-4 h-4" />
              Passengers
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between text-text-primary">
                <span>Adults</span>
                <span>{bookingData.adults}</span>
              </div>
              {bookingData.children > 0 && (
                <div className="flex justify-between text-text-primary">
                  <span>Children</span>
                  <span>{bookingData.children}</span>
                </div>
              )}
            </div>
          </div>

          {/* Car Seats Section */}
          {bookingData.needCarSeats && (
            <div className="bg-surface rounded-lg p-4">
              <div className="flex items-center gap-2 text-sm text-text-primary mb-3">
                <CheckBadgeIcon className="w-4 h-4" />
                Car Seats
              </div>
              <div className="space-y-2 text-sm">
                {bookingData.infantSeats > 0 && (
                  <div className="flex justify-between text-text-primary">
                    <span>Infant (0-1 year)</span>
                    <span>{bookingData.infantSeats}</span>
                  </div>
                )}
                {bookingData.toddlerSeats > 0 && (
                  <div className="flex justify-between text-text-primary">
                    <span>Toddler (1-3 years)</span>
                    <span>{bookingData.toddlerSeats}</span>
                  </div>
                )}
                {bookingData.boosterSeats > 0 && (
                  <div className="flex justify-between text-text-primary">
                    <span>Booster (4-7 years)</span>
                    <span>{bookingData.boosterSeats}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="relative">
      {/* Mobile Header */}
      {!inPanel && (
        <div>
          {/* Mobile title with sticky header */}
          <div className="block md:hidden">
            <div className="sticky top-0 z-[9999]">
              <div className="bg-surface-dark/95 backdrop-blur-md border-b border-white/10">
                <div className="px-4 py-4">
                  <div
                    className={cn(
                      "relative rounded-lg p-4",
                      "transition-all duration-300 ease-in-out",
                      isUpdating ? "bg-primary/10" : "bg-surface"
                    )}
                  >
                    <div className="flex items-center justify-between">
                      <h2 className="text-xl font-light text-text-primary">Trip Summary</h2>
                      {isUpdating && (
                        <div className="flex items-center gap-2">
                          <div className="animate-ping h-2 w-2 rounded-full bg-primary" />
                          <span className="text-sm font-medium text-text-primary">Updating...</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Spacer to prevent content jump */}
          <div className="h-[88px] md:hidden" />

          {/* Desktop-only title */}
          <div className="hidden md:block mb-6">
            <h2 className="text-xl font-light text-text-primary">Trip Summary</h2>
          </div>
        </div>
      )}

      {/* Render mobile or desktop view based on screen size */}
      {renderMobileTripSummary()}
      {renderDesktopTripSummary()}
    </div>
  );
};

// Error boundary wrapper
const TripSummaryWithErrorBoundary = ({ inPanel = false }: TripSummaryProps) => (
  <ErrorBoundary
    fallback={
      <div className="p-4 rounded-lg bg-neutral-900/50">
        <h3 className="text-lg font-medium text-white mb-2">Trip Summary Unavailable</h3>
        <p className="text-white/70 text-sm">
          Unable to display trip summary. Your booking details are still saved.
        </p>
      </div>
    }
  >
    <TripSummary inPanel={inPanel} />
  </ErrorBoundary>
);

export { TripSummaryWithErrorBoundary as default }; 