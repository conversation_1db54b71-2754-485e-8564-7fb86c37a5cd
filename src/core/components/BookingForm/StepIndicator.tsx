import React from 'react';
import { useBookingStore } from '@core/store/bookingStore';

interface Step {
  number: number;
  label: string;
}

const steps: Step[] = [
  { number: 1, label: 'Trip Details' },
  { number: 2, label: 'Select Vehicle' },
  { number: 3, label: 'Request Quote' }
];

export const StepIndicator = () => {
  const { currentStep } = useBookingStore();

  return (
    <div className="relative flex justify-between items-center mb-8">
      {/* Progress Line */}
      <div className="absolute left-0 right-0 top-4 flex">
        {/* First segment */}
        <div className="w-1/2 h-0.5 bg-white/10">
          <div
            className={`h-full bg-primary transition-all duration-300 ${currentStep === 1 ? 'w-1/2' : currentStep > 1 ? 'w-full' : 'w-0'
              }`}
          />
        </div>
        {/* Second segment */}
        <div className="w-1/2 h-0.5 bg-white/10">
          <div
            className={`h-full bg-primary transition-all duration-300 ${currentStep === 2 ? 'w-1/2' : currentStep > 2 ? 'w-full' : 'w-0'
              }`}
          />
        </div>
      </div>

      {/* Steps */}
      {steps.map((step) => (
        <div
          key={step.number}
          className={`relative flex flex-col items-center ${step.number <= currentStep ? 'text-theme-primary' : 'text-theme-muted'
            }`}
        >
          {/* Step Circle */}
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center mb-2 border-2 transition-colors ${step.number < currentStep
              ? 'border-primary bg-primary text-white'
              : step.number === currentStep
                ? 'border-primary bg-surface text-primary'
                : 'border-theme-muted bg-surface text-theme-muted'
              }`}
          >
            {step.number < currentStep ? '✓' : step.number}
          </div>

          {/* Step Label */}
          <span className="text-sm font-medium whitespace-nowrap">{step.label}</span>
        </div>
      ))}
    </div>
  );
}; 