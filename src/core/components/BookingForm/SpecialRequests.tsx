import { useState } from 'react';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';
import { PlusIcon, MinusIcon } from '@heroicons/react/24/outline';

type SpecialRequest = {
  type: string;
  details: string;
};

type SpecialRequestsProps = {
  value: SpecialRequest[];
  onChange: (requests: SpecialRequest[]) => void;
};

const requestTypes = [
  'Dietary Requirements',
  'Accessibility Needs',
  'Additional Stops',
  'Child Seats',
  'Luggage Assistance',
  'Other'
];

export const SpecialRequests = ({ value = [], onChange }: SpecialRequestsProps) => {
  const [selectedType, setSelectedType] = useState('');
  const [details, setDetails] = useState('');

  const handleAdd = () => {
    if (selectedType && details) {
      onChange([...value, { type: selectedType, details }]);
      setSelectedType('');
      setDetails('');
    }
  };

  const handleRemove = (index: number) => {
    onChange(value.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-4">
      <h4 className="text-lg font-medium text-white">Special Requests</h4>

      {/* Existing Requests */}
      {value.length > 0 && (
        <div className="space-y-3">
          {value.map((request, index) => (
            <div 
              key={index}
              className="flex items-start justify-between bg-surface rounded-lg p-3"
            >
              <div className="flex-1">
                <p className="text-sm font-medium text-white">{request.type}</p>
                <p className="text-sm text-neutral-400">{request.details}</p>
              </div>
              <button
                onClick={() => handleRemove(index)}
                className="p-1 text-neutral-400 hover:text-white"
              >
                <MinusIcon className="w-5 h-5" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Add New Request */}
      <div className="grid grid-cols-2 gap-4">
        <select
          value={selectedType}
          onChange={(e) => setSelectedType(e.target.value)}
          className="bg-neutral-800/50 border border-white/10 rounded-lg px-4 py-2 text-white"
        >
          <option value="">Select Request Type</option>
          {requestTypes.map((type) => (
            <option key={type} value={type}>{type}</option>
          ))}
        </select>

        <div className="flex gap-2">
          <Input
            placeholder="Enter details"
            value={details}
            onChange={(e) => setDetails(e.target.value)}
          />
          <Button
            onClick={handleAdd}
            disabled={!selectedType || !details}
            className="flex-shrink-0"
          >
            <PlusIcon className="w-5 h-5" />
          </Button>
        </div>
      </div>
    </div>
  );
};
