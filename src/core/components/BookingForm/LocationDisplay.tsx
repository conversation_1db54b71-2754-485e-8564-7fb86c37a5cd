import React from 'react';
import { Location } from '@core/types/booking';
import { MapPinIcon } from '@heroicons/react/24/outline';

interface LocationDisplayProps {
  location: Location | null;
  label: string;
  type: 'pickup' | 'stop' | 'dropoff';
}

export const LocationDisplay: React.FC<LocationDisplayProps> = ({ location, label, type }) => {
  if (!location || !location.address) return null;

  const parts = location.address.split(',');

  return (
    <div className="flex items-start gap-2">
      <div className={`flex-shrink-0 w-5 h-5 rounded-full border border-primary/60 bg-surface flex items-center justify-center`}>
        <span className="text-xs text-primary">{label}</span>
      </div>
      <div className="flex-grow">
        {parts.map((part, i) => (
          <span key={i} className={i === 0 ? "text-primary font-medium" : "text-primary/70 text-sm"}>
            {part.trim()}
            {i < parts.length - 1 && ','}<br />
          </span>
        ))}
      </div>
    </div>
  );
}; 