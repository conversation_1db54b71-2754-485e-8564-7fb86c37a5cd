import { config } from '@core/config/env';
import { bookingDebugger } from '@core/utils/debugLogger';

export interface HereLocation {
  id: string;
  name: string;
  address: {
    label: string;
    countryCode?: string;
    countryName?: string;
    stateCode?: string;
    state?: string;
    county?: string;
    city?: string;
    district?: string;
    street?: string;
    postalCode?: string;
    houseNumber?: string;
  };
  coordinates: [number, number];
  type: string;
  iata?: string;
  distance?: number;
  details?: {
    terminal?: string;
    category?: string[];
  };
  mapView?: {
    west: number;
    south: number;
    east: number;
    north: number;
  };
}

interface HereSearchResponse {
  items: Array<{
    id: string;
    title: string;
    address: {
      label: string;
      countryCode?: string;
      countryName?: string;
      stateCode?: string;
      state?: string;
      county?: string;
      city?: string;
      district?: string;
      street?: string;
      postalCode?: string;
      houseNumber?: string;
    };
    position: {
      lat: number;
      lng: number;
    };
    resultType: string;
    distance?: number;
    categories?: Array<{ id: string; name: string; primary?: boolean }>;
    details?: {
      terminal?: string;
      iata?: string;
    };
    mapView?: {
      west: number;
      south: number;
      east: number;
      north: number;
    };
    houseNumberType?: string;
    scoring?: {
      queryScore: number;
      fieldScore: {
        city?: number;
        streets?: number[];
        houseNumber?: number;
      };
    };
  }>;
}

interface SearchOptions {
  isAirportSearch?: boolean;
  lat?: number;
  lng?: number;
  categories?: string[];
  language?: string;
  limit?: number;
  offset?: number;
  location?: {
    lat: number;
    lng: number;
  };
}

// Common categories for easy reference
export const PLACE_CATEGORIES = {
  HOTEL: 'hotel',
  RESTAURANT: 'restaurant',
  ATTRACTION: 'tourist-attraction',
  SHOPPING: 'shopping',
  TRANSPORT: 'transport',
  BUSINESS: 'business',
  ENTERTAINMENT: 'entertainment'
} as const;

// Add version tracking
const HERE_API_VERSION = "2.0.1"; // Increment this when making significant changes
console.log(`[HERE API] Loading HERE API service version ${HERE_API_VERSION}`);

interface HereApiItem {
  id: string;
  title: string;
  address: {
    label: string;
    countryCode: string;
    countryName: string;
    stateCode?: string;
    state?: string;
    county?: string;
    city?: string;
    district?: string;
    street?: string;
    postalCode?: string;
    houseNumber?: string;
  };
  position: {
    lat: number;
    lng: number;
  };
  resultType: string;
}

function transformHereLocation(item: HereApiItem): HereLocation {
  return {
    id: item.id,
    name: item.title,
    address: item.address,
    coordinates: [item.position.lng, item.position.lat],
    type: item.resultType
  };
}

// Get API key from WordPress config if available
export const getApiKey = () => {
  console.log('[HERE API] Getting API key...');
  if (typeof window !== 'undefined' && 'limoBookingConfig' in window) {
    console.log('[HERE API] Found limoBookingConfig in window');
    const wpConfig = (window as any).limoBookingConfig;
    const apiKey = wpConfig?.hereApiKey || config.hereApiKey;
    console.log('[HERE API] Using API key from:', wpConfig?.hereApiKey ? 'WordPress' : 'config');
    return apiKey;
  }
  console.log('[HERE API] Using API key from config');
  return config.hereApiKey;
};

export async function searchLocations(query: string, options?: SearchOptions): Promise<HereLocation[]> {
  const hereApiKey = getApiKey();
  
  console.log('[HERE API] Search initiated with query:', query);
  console.log('[HERE API] API Key present:', Boolean(hereApiKey));
  console.log('[HERE API] Search options:', { ...options, apiKey: '[REDACTED]' });
  
  if (!hereApiKey) {
    const error = new Error('HERE API key is not configured');
    console.error('[HERE API] Error:', error.message);
    throw error;
  }

  try {
    const params = new URLSearchParams({
      q: query,
      apiKey: hereApiKey,
      limit: String(options?.limit || 5),
      lang: options?.language || 'en',
    });

    if (options?.location) {
      params.append('at', `${options.location.lat},${options.location.lng}`);
      bookingDebugger.log('debug', 'HERE API', `Using location context: ${options.location.lat},${options.location.lng}`);
    } else {
      bookingDebugger.log('debug', 'HERE API', 'No location context provided, results may be less relevant');
    }

    const url = `https://geocode.search.hereapi.com/v1/geocode?${params.toString()}`;
    const redactedUrl = url.replace(hereApiKey, '[REDACTED]');
    bookingDebugger.log('debug', 'HERE API', `Making request to: ${redactedUrl}`);

    const startTime = Date.now();
    const response = await fetch(url);
    const endTime = Date.now();

    bookingDebugger.log('debug', 'HERE API', `Response received in ${endTime - startTime}ms`);
    bookingDebugger.log('debug', 'HERE API', `Response status: ${response.status}`);
    bookingDebugger.log('debug', 'HERE API', 'Response headers:', Object.fromEntries(response.headers.entries()));
    
    const data = await response.json();

    if (!response.ok) {
      const error = new Error(`HERE API error: ${data.error || response.statusText}`);
      bookingDebugger.log('error', 'HERE API', error.message, {
        status: response.status,
        statusText: response.statusText,
        error: data.error,
        details: data
      });
      throw error;
    }

    if (!data.items || !Array.isArray(data.items)) {
      bookingDebugger.log('warn', 'HERE API', 'No results found or invalid response format', {
        responseType: typeof data,
        hasItems: 'items' in data,
        itemsType: data.items ? typeof data.items : 'undefined'
      });
      return [];
    }

    bookingDebugger.log('info', 'HERE API', `Found ${data.items.length} locations`);
    bookingDebugger.log('debug', 'HERE API', 'First result:', data.items[0]);
    
    const results = data.items.map(transformHereLocation);
    bookingDebugger.log('debug', 'HERE API', `Transformed ${results.length} results`);
    
    return results;
  } catch (error) {
    bookingDebugger.log('error', 'HERE API', 'Failed to search locations', {
      error,
      query,
      options: { ...options, apiKey: '[REDACTED]' }
    });
    throw error;
  }
}

export async function getPlaceDetails(id: string): Promise<HereLocation> {
  try {
    const hereApiKey = getApiKey();
    
    if (!hereApiKey) {
      throw new Error('HERE API key is not configured');
    }
    
    const response = await fetch(
      `https://lookup.search.hereapi.com/v1/lookup?id=${id}&apiKey=${hereApiKey}`
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`HERE API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
    }

    const data = await response.json();
    return {
      id: data.id,
      name: data.title,
      address: {
        label: data.address.label,
        countryCode: data.address.countryCode,
        countryName: data.address.countryName,
        stateCode: data.address.stateCode,
        state: data.address.state,
        county: data.address.county,
        city: data.address.city,
        district: data.address.district,
        street: data.address.street,
        postalCode: data.address.postalCode,
        houseNumber: data.address.houseNumber
      },
      coordinates: [data.position.lng, data.position.lat],
      type: data.resultType || 'place',
      iata: data.details?.iata,
      details: {
        terminal: data.details?.terminal,
        category: data.categories
      },
      mapView: data.mapView
    };
  } catch (error) {
    console.error('HERE API lookup error:', error);
    throw error;
  }
}

/**
 * Get city name from coordinates using HERE API
 * @version 2.0.0 - Fixed coordinate validation and auto-correction
 * @param coordinates [longitude, latitude] array
 * @returns City name or null if not found
 */
export const getCityFromCoordinates = async (coordinates: [number, number]): Promise<string | null> => {
  console.log('[HERE API] Version 2.0.0 - getCityFromCoordinates called with:', coordinates);
  
  try {
    // Validate coordinates
    if (!coordinates || !Array.isArray(coordinates) || coordinates.length !== 2) {
      console.error('[HERE API] Invalid coordinates format:', coordinates);
      return null;
    }
    
    // Extract longitude and latitude
    let [longitude, latitude] = coordinates;
    
    // Check if coordinates might be in the wrong order (latitude, longitude)
    // Longitude should be between -180 and 180, latitude between -90 and 90
    if (Math.abs(longitude) > 90 && Math.abs(latitude) <= 180) {
      // Coordinates are likely in the wrong order, swap them
      console.log('[HERE API] Coordinates appear to be in wrong order, auto-correcting');
      [longitude, latitude] = [latitude, longitude];
    }
    
    // Final validation check
    if (Math.abs(longitude) > 180 || Math.abs(latitude) > 90) {
      console.error('[HERE API] Coordinates out of valid range after correction:', { longitude, latitude });
      return null;
    }
    
    console.log('[HERE API] Reverse geocoding coordinates:', { latitude, longitude });
    
    // Get HERE API key using the common getApiKey function
    const hereApiKey = getApiKey();
    
    if (!hereApiKey) {
      console.error('[HERE API] API key not found');
      return null;
    }
    
    // Construct the URL for the HERE API
    // NOTE: HERE API expects coordinates in the format "latitude,longitude"
    const url = `https://revgeocode.search.hereapi.com/v1/revgeocode?at=${latitude},${longitude}&lang=en-US&apiKey=${hereApiKey}`;
    
    console.log('[HERE API] Making API request...');
    
    // Make the request
    const response = await fetch(url);
    
    if (!response.ok) {
      console.error('[HERE API] API request failed:', { status: response.status, statusText: response.statusText });
      return null;
    }
    
    const data = await response.json();
    
    // Check if we have items in the response
    if (!data.items || data.items.length === 0) {
      console.log('[HERE API] No items found in response');
      return null;
    }
    
    // Get the first item
    const item = data.items[0];
    
    // Try to extract city from address
    if (item.address && item.address.city) {
      console.log('[HERE API] City found in address:', item.address.city);
      return item.address.city;
    }
    
    // If city is not available, try to extract from district or county
    if (item.address && item.address.district) {
      console.log('[HERE API] District found in address:', item.address.district);
      return item.address.district;
    }
    
    if (item.address && item.address.county) {
      console.log('[HERE API] County found in address:', item.address.county);
      return item.address.county;
    }
    
    // If all else fails, try to extract from the address label
    if (item.address && item.address.label) {
      const addressParts = item.address.label.split(',').map((part: string) => part.trim());
      
      // For US addresses, city is typically the second-to-last part
      if (addressParts.length >= 3) {
        const cityPart = addressParts[addressParts.length - 3].trim();
        console.log('[HERE API] City extracted from address label:', cityPart);
        return cityPart;
      }
    }
    
    console.log('[HERE API] No city found in response');
    return null;
  } catch (error) {
    console.error('[HERE API] Error getting city from coordinates:', error);
    return null;
  }
};