/**
 * CSS Logger - A performance-oriented logging system for CSS analysis
 * 
 * Features:
 * - Logs applied CSS properties with component context
 * - Tracks CSS changes over time
 * - Measures performance impact of style applications
 * - Provides filtering by component, property or selector
 * - Low overhead implementation with buffering for production use
 */

interface CSSLogEntry {
  componentId: string;
  timestamp: number;
  selector: string;
  properties: Record<string, string>;
  applyTime?: number; // Time taken to apply styles in ms
  source?: 'inline' | 'class' | 'stylesheet' | 'theme';
  stackTrace?: string;
}

interface CSSLogOptions {
  maxEntries?: number;
  logToConsole?: boolean;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
  enablePerformanceTracking?: boolean;
  enableStackTraces?: boolean;
  bufferSize?: number;
}

class CSSLogger {
  private logs: CSSLogEntry[] = [];
  private buffer: CSSLogEntry[] = [];
  private options: Required<CSSLogOptions>;
  private flushTimeout: NodeJS.Timeout | null = null;
  private startTime: number = performance.now();
  private componentStylesMap: Map<string, Record<string, string>> = new Map();
  private propertyChangeFrequency: Record<string, number> = {};
  private isEnabled: boolean = true;

  constructor(options: CSSLogOptions = {}) {
    this.options = {
      maxEntries: options.maxEntries ?? 1000,
      logToConsole: options.logToConsole ?? false,
      logLevel: options.logLevel ?? 'info',
      enablePerformanceTracking: options.enablePerformanceTracking ?? true,
      enableStackTraces: options.enableStackTraces ?? false,
      bufferSize: options.bufferSize ?? 20
    };

    // Check if we're in development mode
    if (import.meta.env.DEV) {
      this.options.logToConsole = true;
      this.options.enableStackTraces = true;
    }

    if (typeof window !== 'undefined') {
      // Add global access for debugging in console
      (window as any).__cssLogger = this;
      
      // Expose control methods
      (window as any).toggleCSSLogging = this.toggle.bind(this);
      (window as any).clearCSSLogs = this.clear.bind(this);
      (window as any).analyzeCSSPerformance = this.analyzePerformance.bind(this);
    }
  }

  /**
   * Log a CSS application event with detailed context
   */
  public logStyle(
    componentId: string,
    selector: string,
    properties: Record<string, string>,
    source: 'inline' | 'class' | 'stylesheet' | 'theme' = 'inline'
  ): void {
    if (!this.isEnabled) return;

    const start = this.options.enablePerformanceTracking ? performance.now() : 0;
    
    const entry: CSSLogEntry = {
      componentId,
      timestamp: Date.now(),
      selector,
      properties,
      source
    };

    // Track property change frequency
    Object.keys(properties).forEach(prop => {
      this.propertyChangeFrequency[prop] = (this.propertyChangeFrequency[prop] || 0) + 1;
    });

    // Add stack trace in development mode
    if (this.options.enableStackTraces) {
      try {
        throw new Error('CSS Logger Stack Trace');
      } catch (err) {
        entry.stackTrace = (err as Error).stack?.split('\n').slice(2).join('\n');
      }
    }

    if (this.options.enablePerformanceTracking) {
      entry.applyTime = performance.now() - start;
    }

    // Update component styles map
    const existingStyles = this.componentStylesMap.get(componentId) || {};
    this.componentStylesMap.set(componentId, {
      ...existingStyles,
      ...properties
    });

    // Add to buffer
    this.buffer.push(entry);
    
    // Log to console if enabled
    if (this.options.logToConsole) {
      console.log(`[CSSLogger] ${componentId} - ${selector}`, properties);
    }

    // Schedule buffer flush
    this.scheduleFlush();
  }

  /**
   * Log theme-based styles
   */
  public logThemeStyles(
    componentId: string,
    properties: Record<string, string>
  ): void {
    this.logStyle(componentId, 'theme-variables', properties, 'theme');
  }

  /**
   * Schedule a buffer flush to main logs
   */
  private scheduleFlush(): void {
    if (this.flushTimeout !== null) {
      clearTimeout(this.flushTimeout);
    }

    if (this.buffer.length >= this.options.bufferSize) {
      this.flush();
    } else {
      this.flushTimeout = setTimeout(() => this.flush(), 1000);
    }
  }

  /**
   * Flush buffered logs to main logs array
   */
  private flush(): void {
    this.logs = [...this.logs, ...this.buffer];
    
    // Trim logs if they exceed max entries
    if (this.logs.length > this.options.maxEntries) {
      this.logs = this.logs.slice(this.logs.length - this.options.maxEntries);
    }
    
    this.buffer = [];
    this.flushTimeout = null;
  }

  /**
   * Get all logs with optional filtering
   */
  public getLogs(
    filter?: {
      componentId?: string;
      property?: string;
      selector?: string;
      timeRange?: [number, number];
      source?: 'inline' | 'class' | 'stylesheet' | 'theme';
    }
  ): CSSLogEntry[] {
    // Make sure buffer is flushed
    this.flush();
    
    if (!filter) return this.logs;
    
    return this.logs.filter(entry => {
      if (filter.componentId && entry.componentId !== filter.componentId) return false;
      if (filter.selector && entry.selector !== filter.selector) return false;
      if (filter.source && entry.source !== filter.source) return false;
      if (filter.property && !Object.keys(entry.properties).includes(filter.property)) return false;
      if (filter.timeRange && (entry.timestamp < filter.timeRange[0] || entry.timestamp > filter.timeRange[1])) return false;
      return true;
    });
  }

  /**
   * Clear all logs
   */
  public clear(): void {
    this.logs = [];
    this.buffer = [];
    this.propertyChangeFrequency = {};
    this.componentStylesMap = new Map();
    console.log('[CSSLogger] Logs cleared');
  }

  /**
   * Toggle logging on/off
   */
  public toggle(): void {
    this.isEnabled = !this.isEnabled;
    console.log(`[CSSLogger] Logging ${this.isEnabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Analyze performance metrics from logs
   */
  public analyzePerformance(): Record<string, any> {
    // Ensure all buffered logs are included
    this.flush();
    
    if (!this.options.enablePerformanceTracking) {
      console.warn('[CSSLogger] Performance tracking is disabled');
      return {};
    }

    const totalEntries = this.logs.length;
    const timeSpan = Date.now() - this.startTime;
    
    // Calculate average apply time
    let totalApplyTime = 0;
    let maxApplyTime = 0;
    let slowestComponent = '';
    
    this.logs.forEach(entry => {
      if (entry.applyTime) {
        totalApplyTime += entry.applyTime;
        
        if (entry.applyTime > maxApplyTime) {
          maxApplyTime = entry.applyTime;
          slowestComponent = entry.componentId;
        }
      }
    });
    
    // Most frequently changed properties
    const topChangedProperties = Object.entries(this.propertyChangeFrequency)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([prop, count]) => ({ property: prop, count }));
    
    // Components with most style changes
    const componentChangeCounts: Record<string, number> = {};
    this.logs.forEach(entry => {
      componentChangeCounts[entry.componentId] = (componentChangeCounts[entry.componentId] || 0) + 1;
    });
    
    const topChangedComponents = Object.entries(componentChangeCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([comp, count]) => ({ component: comp, count }));
    
    const results = {
      totalLogEntries: totalEntries,
      totalTimeSpan: `${(timeSpan / 1000).toFixed(2)}s`,
      averageApplyTime: totalApplyTime / totalEntries,
      maxApplyTime,
      slowestComponent,
      uniqueComponents: this.componentStylesMap.size,
      topChangedProperties,
      topChangedComponents,
      changeFrequency: `${(totalEntries / (timeSpan / 1000)).toFixed(2)} changes/sec`
    };
    
    console.table(results);
    return results;
  }
  
  /**
   * Export logs to JSON for external analysis
   */
  public exportLogs(): string {
    this.flush();
    return JSON.stringify({
      logs: this.logs,
      metadata: {
        totalEntries: this.logs.length,
        startTime: this.startTime,
        endTime: Date.now(),
        propertyChangeFrequency: this.propertyChangeFrequency,
        uniqueComponents: this.componentStylesMap.size
      }
    });
  }
  
  /**
   * Find excessive style recalculations
   */
  public findStyleThrashing(): Record<string, number> {
    const componentChangeFrequency: Record<string, number> = {};
    const THRESHOLD_TIME_MS = 50; // 50ms threshold to identify rapid changes
    
    // Count rapid changes for each component
    for (let i = 1; i < this.logs.length; i++) {
      const current = this.logs[i];
      const previous = this.logs[i-1];
      
      if (
        current.componentId === previous.componentId && 
        current.timestamp - previous.timestamp < THRESHOLD_TIME_MS
      ) {
        componentChangeFrequency[current.componentId] = 
          (componentChangeFrequency[current.componentId] || 0) + 1;
      }
    }
    
    return componentChangeFrequency;
  }
}

// Export singleton instance
export const cssLogger = new CSSLogger(); 