@layer components {
  .react-datepicker-wrapper {
    @apply w-full !important;
  }

  .react-datepicker-wrapper input {
    @apply h-12 !important;
  }

  /* Base calendar styles */
  .react-datepicker {
    @apply bg-surface border border-border rounded-lg shadow-lg !important;
    font-size: 1rem !important;
    display: flex !important;
  }

  .react-datepicker-popper {
    z-index: var(--z-datepicker) !important;
  }

  /* Header styles */
  .react-datepicker__header {
    @apply bg-surface border-b border-border !important;
    padding: 0.5rem 0 0.75rem !important;
    height: auto !important;
  }

  .react-datepicker__current-month,
  .react-datepicker-time__header {
    @apply text-text-primary !important;
    font-size: 1rem !important;
    margin-bottom: 0.75rem !important;
  }

  /* Navigation buttons */
  .react-datepicker__navigation {
    top: 1rem !important;
  }

  .react-datepicker__navigation-icon {
    @apply text-text-primary !important;
  }

  .react-datepicker__navigation-icon::before {
    border-color: var(--text-primary) !important;
    border-width: 2px 2px 0 0 !important;
  }

  /* Week days row */
  .react-datepicker__day-names {
    display: flex !important;
    justify-content: center !important;
    margin: 0 !important;
  }

  .react-datepicker__day-name {
    @apply w-10 m-0.5 !important;
    height: 24px !important;
    line-height: 24px !important;
    @apply text-text-muted !important;
    font-size: 0.9rem !important;
  }

  /* Month container */
  .react-datepicker__month {
    margin: 0 !important;
    padding-top: 0.5rem !important;
    @apply bg-surface !important;
  }

  /* Day styles */
  .react-datepicker__day {
    @apply w-10 h-10 leading-10 m-0.5 rounded !important;
    font-size: 0.9rem !important;
    color: var(--text-primary) !important;
    position: relative !important;
    z-index: 1 !important;
  }

  .react-datepicker__day:hover {
    background-color: var(--primary) !important;
    color: #ffffff !important;
  }

  .react-datepicker__day--selected,
  .react-datepicker__day--keyboard-selected {
    background-color: var(--primary) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
  }

  .react-datepicker__day--disabled {
    @apply text-text-disabled cursor-not-allowed !important;
  }

  .react-datepicker__day--disabled:hover {
    background-color: transparent !important;
    color: var(--text-disabled) !important;
  }

  .react-datepicker__day--outside-month {
    @apply text-text-disabled/50 !important;
  }

  /* Time container */
  .react-datepicker__time-container {
    @apply border-l border-border !important;
    width: 110px !important;
    margin-left: 0 !important;
    float: right !important;
  }

  .react-datepicker-time__header {
    @apply bg-surface border-b border-border !important;
    padding: 0.5rem 0 !important;
    height: 38px !important;
    margin: 0 !important;
  }

  .react-datepicker__time-container .react-datepicker__time {
    @apply bg-surface !important;
    height: 296px !important;
  }

  .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box {
    width: 110px !important;
    height: 100% !important;
    margin: 0 !important;
  }

  /* Time list */
  .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list {
    height: 100% !important;
    padding: 0 !important;
    overflow-y: auto !important;
  }

  /* Scrollbar styles */
  .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list::-webkit-scrollbar {
    width: 6px !important;
  }

  .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list::-webkit-scrollbar-track {
    @apply bg-surface-dark !important;
  }

  .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list::-webkit-scrollbar-thumb {
    @apply bg-primary rounded-full !important;
  }

  /* Time list items */
  .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item {
    @apply text-text-primary !important;
    height: 40px !important;
    line-height: 40px !important;
    font-size: 0.9rem !important;
    padding: 0 10px !important;
  }

  .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover {
    background-color: var(--primary) !important;
    color: #ffffff !important;
  }

  .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected {
    background-color: var(--primary) !important;
    color: #ffffff !important;
  }

  /* Calendar icon styles */
  .calendar-icon {
    @apply text-text-primary opacity-70 !important;
  }

  .calendar-icon svg {
    @apply w-5 h-5 !important;
    color: var(--text-primary) !important;
    fill: none !important;
    stroke: currentColor !important;
    stroke-width: 2 !important;
  }

  /* Input field styles */
  .react-datepicker__input-container input {
    @apply bg-surface text-text-primary !important;
    border: 1px solid var(--border) !important;
  }

  .react-datepicker__input-container input:focus {
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 2px var(--primary-transparent) !important;
  }

  /* WordPress specific overrides */
  html[data-wp] .react-datepicker__day:hover {
    background-color: var(--primary) !important;
    color: #ffffff !important;
  }

  html[data-wp] .react-datepicker__day--selected,
  html[data-wp] .react-datepicker__day--keyboard-selected {
    background-color: var(--primary) !important;
    color: #ffffff !important;
  }

  html[data-wp] .calendar-icon svg {
    color: var(--text-primary) !important;
    fill: none !important;
    stroke: currentColor !important;
  }

  /* Force light icons in WordPress */
  html[data-wp] .react-datepicker__navigation-icon::before {
    border-color: var(--text-primary) !important;
    opacity: 0.7 !important;
  }

  html[data-wp] .react-datepicker-wrapper svg,
  html[data-wp] .react-datepicker svg {
    color: var(--text-primary) !important;
    fill: none !important;
    stroke: currentColor !important;
    opacity: 0.7 !important;
  }
}