export interface FlightDetails {
  flightNumber: string;
  airline: string;
  status: string;
  departure: {
    airport: string;
    terminal: string;
    gate: string;
    time: string;
  };
  arrival: {
    airport: string;
    terminal: string;
    gate: string;
    time: string;
  };
  weather: {
    departure: {
      temp: string;
      condition: string;
    };
    arrival: {
      temp: string;
      condition: string;
    };
  };
  aircraft: string;
  duration: string;
  distance: string;
  baggageClaim: string;
  onTimePerformance: number;
} 