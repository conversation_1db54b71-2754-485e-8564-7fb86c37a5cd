export interface ColorScheme {
  primary: string;
  primaryLight: string;
  primaryDark: string;
}

export interface BackgroundMode {
  background: string;
  surface: string;
  surfaceDark: string;
}

export interface FontColors {
  textPrimary: string;
  textSecondary: string;
  textMuted: string;
  textDisabled: string;
}

export interface ThemeConfig {
  colorScheme: ColorScheme;
  backgroundMode: BackgroundMode;
  fontColors: FontColors;
} 