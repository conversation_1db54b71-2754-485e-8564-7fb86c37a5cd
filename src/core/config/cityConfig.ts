export interface CityConfig {
  bbox?: string;
  country?: string;
  name: string;
}

export const cityConfigs: Record<string, CityConfig> = {
  paris: {
    name: 'Paris',
    bbox: '2.2241,48.8156,2.4699,48.9021',
    country: 'fr'
  },
  london: {
    name: 'London',
    bbox: '-0.5103,51.2868,0.3340,51.6919',
    country: 'gb'
  },
  newyork: {
    name: 'New York',
    bbox: '-74.2591,40.4774,-73.7004,40.9176',
    country: 'us'
  },
  // Default US configuration (no city restriction)
  worldwide: {
    name: 'Worldwide',
    // No bbox or country restrictions for worldwide search
  }
}; 