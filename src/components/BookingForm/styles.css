.booking-form {
  background-color: var(--surface);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  color: var(--text-primary);
}

.booking-form__header {
  border-bottom: 1px solid var(--border);
  padding-bottom: 1rem;
  margin-bottom: 1.5rem;
}

.booking-form__title {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.booking-form__subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
}

.booking-form__content {
  min-height: 400px;
}

.booking-form__steps {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.booking-form__step {
  background-color: var(--surface-dark);
  border-radius: 0.375rem;
  padding: 1.25rem;
  border: 1px solid var(--border);
}

.booking-form__step-title {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

.booking-form__footer {
  border-top: 1px solid var(--border);
  padding-top: 1rem;
  margin-top: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Form controls */
.booking-form__input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: 0.375rem;
  background-color: var(--surface);
  color: var(--text-primary);
  transition: border-color 0.2s ease;
}

.booking-form__input:focus {
  outline: none;
  border-color: var(--text-secondary);
}

.booking-form__input::placeholder {
  color: var(--text-secondary);
}

.booking-form__label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
}

/* Responsive styles */
@media (max-width: 768px) {
  .booking-form {
    padding: 1rem;
  }

  .booking-form__step {
    padding: 1rem;
  }

  .booking-form__title {
    font-size: 1.25rem;
  }

  .booking-form__step-title {
    font-size: 1.125rem;
  }
}