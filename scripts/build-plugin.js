#!/usr/bin/env node

/**
 * WordPress Plugin Build Script
 * 
 * This script automates the process of building the WordPress plugin:
 * 1. Runs the TypeScript compiler
 * 2. Builds the React app with Vite
 * 3. Copies all required assets
 * 4. Creates a zip file of the plugin
 */

import { execSync } from 'child_process';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

// Get directory name in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  rootDir: path.resolve(__dirname, '..'),
  buildDir: path.resolve(__dirname, '../wordpress/limo-booking'),
  assetsDir: path.resolve(__dirname, '../new_build/public/assets'),
  targetAssetsDir: path.resolve(__dirname, '../wordpress/limo-booking/public/assets'),
  outputZip: path.resolve(__dirname, '../limo-booking-plugin.zip')
};

// Helper to execute commands
function runCommand(command, options = {}) {
  console.log(chalk.blue(`Running: ${command}`));
  try {
    execSync(command, { 
      stdio: 'inherit', 
      cwd: config.rootDir,
      ...options 
    });
    return true;
  } catch (error) {
    console.error(chalk.red(`Command failed: ${command}`));
    console.error(error.message);
    return false;
  }
}

// Helper to copy assets
function copyAssets() {
  console.log(chalk.blue(`Copying assets from ${config.assetsDir} to ${config.targetAssetsDir}`));
  
  try {
    // Ensure target directory exists
    fs.ensureDirSync(config.targetAssetsDir);
    
    // Copy assets
    fs.copySync(config.assetsDir, config.targetAssetsDir, { overwrite: true });
    console.log(chalk.green('Assets copied successfully'));
    return true;
  } catch (error) {
    console.error(chalk.red('Failed to copy assets:'), error.message);
    return false;
  }
}

// Main build function
async function buildPlugin() {
  console.log(chalk.yellow('=== Building WordPress Plugin ==='));
  
  // Step 1: Build the React app
  console.log(chalk.yellow('\n=== Building React App ==='));
  if (!runCommand('npm run build:wordpress')) {
    console.error(chalk.red('Failed to build React app'));
    process.exit(1);
  }
  
  // Step 2: Copy assets
  console.log(chalk.yellow('\n=== Copying Assets ==='));
  if (!copyAssets()) {
    console.error(chalk.red('Failed to copy assets'));
    process.exit(1);
  }
  
  // Step 3: Create zip file
  console.log(chalk.yellow('\n=== Creating Plugin Zip ==='));
  if (!runCommand(`cd wordpress && zip -r ${config.outputZip} limo-booking`)) {
    console.error(chalk.red('Failed to create plugin zip'));
    process.exit(1);
  }
  
  console.log(chalk.green('\n=== Build Completed Successfully ==='));
  console.log(chalk.green(`Plugin zip created at: ${config.outputZip}`));
}

// Run the build
buildPlugin().catch(error => {
  console.error(chalk.red('Build failed:'), error);
  process.exit(1);
}); 