#!/usr/bin/env node

/**
 * WordPress Plugin Build Script
 * 
 * This script automates the process of building the WordPress plugin:
 * 1. Runs the TypeScript compiler
 * 2. Builds the React app with Vite
 * 3. Creates a zip file of the plugin
 */

import { execSync } from 'child_process';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

// Get directory name in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  rootDir: path.resolve(__dirname, '..'),
  buildDir: path.resolve(__dirname, '../wordpress/limo-booking'),
  outputZip: path.resolve(__dirname, '../wordpress/limo-booking.zip')
};

// Helper to execute commands
function runCommand(command, options = {}) {
  console.log(chalk.blue(`Running: ${command}`));
  try {
    execSync(command, { 
      stdio: 'inherit', 
      cwd: config.rootDir,
      ...options 
    });
    return true;
  } catch (error) {
    console.error(chalk.red(`Command failed: ${command}`));
    console.error(error.message);
    return false;
  }
}

// Main build function
async function buildPlugin() {
  console.log(chalk.yellow('=== Building WordPress Plugin ==='));
  
  // Step 1: Build the React app
  console.log(chalk.yellow('\n=== Building React App ==='));
  if (!runCommand('npm run build:wordpress')) {
    console.error(chalk.red('Failed to build React app'));
    process.exit(1);
  }

  // Step 2: Ensure all necessary files are in place
  console.log(chalk.yellow('\n=== Verifying Plugin Files ==='));
  const publicDir = path.resolve(config.buildDir, 'public');
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
    console.log(chalk.blue(`Created public directory: ${publicDir}`));
  }
  
  // Step 3: Create zip file
  console.log(chalk.yellow('\n=== Creating Plugin Zip ==='));
  if (!runCommand(`cd wordpress && zip -r limo-booking.zip limo-booking`)) {
    console.error(chalk.red('Failed to create plugin zip'));
    process.exit(1);
  }
  
  console.log(chalk.green('\n=== Build Completed Successfully ==='));
  console.log(chalk.green(`Plugin zip created at: ${config.outputZip}`));
}

// Run the build
buildPlugin().catch(error => {
  console.error(chalk.red('Build failed:'), error);
  process.exit(1);
});