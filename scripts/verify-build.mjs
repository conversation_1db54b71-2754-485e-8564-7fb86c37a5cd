import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const pluginDir = path.resolve(__dirname, '../wordpress/limo-booking');

async function verifyBuild() {
    console.log('=== Verifying Limo Booking Plugin Build ===\n');

    // Check critical files
    const criticalFiles = [
        'limo-booking.php',
        'public/wordpress.js',
        'public/wordpress.css',
        'includes/class-react-integration.php',
        'admin/class-limo-booking-theme-manager.php'
    ];

    console.log('Checking critical files:');
    for (const file of criticalFiles) {
        const filePath = path.join(pluginDir, file);
        const exists = await fs.pathExists(filePath);
        console.log(`${exists ? '✓' : '✗'} ${file}`);
        
        if (exists) {
            const stats = await fs.stat(filePath);
            console.log(`   Size: ${stats.size} bytes`);
            console.log(`   Last modified: ${stats.mtime}`);
        }
    }

    // Check directories
    const criticalDirs = [
        'public',
        'includes',
        'admin',
        'templates',
        'languages'
    ];

    console.log('\nChecking critical directories:');
    for (const dir of criticalDirs) {
        const dirPath = path.join(pluginDir, dir);
        const exists = await fs.pathExists(dirPath);
        console.log(`${exists ? '✓' : '✗'} ${dir}/`);
        
        if (exists) {
            const files = await fs.readdir(dirPath);
            console.log(`   Files: ${files.length}`);
            if (files.length > 0) {
                console.log('   Contents:');
                files.forEach(file => {
                    if (file !== '.' && file !== '..') {
                        console.log(`   - ${file}`);
                    }
                });
            }
        }
    }

    // Check main plugin file content
    console.log('\nVerifying main plugin file:');
    const mainPluginPath = path.join(pluginDir, 'limo-booking.php');
    if (await fs.pathExists(mainPluginPath)) {
        const content = await fs.readFile(mainPluginPath, 'utf8');
        
        // Check for required components
        const checks = {
            'Plugin header': /Plugin Name:/,
            'Version constant': /LIMO_BOOKING_VERSION/,
            'Shortcode registration': /add_shortcode/,
            'Script enqueuing': /wp_enqueue_script/,
            'Style enqueuing': /wp_enqueue_style/
        };

        for (const [name, regex] of Object.entries(checks)) {
            const found = regex.test(content);
            console.log(`${found ? '✓' : '✗'} ${name}`);
        }
    }

    // Check build artifacts
    console.log('\nChecking build artifacts:');
    const buildArtifacts = [
        'public/wordpress.js',
        'public/wordpress.css'
    ];

    for (const artifact of buildArtifacts) {
        const artifactPath = path.join(pluginDir, artifact);
        const exists = await fs.pathExists(artifactPath);
        console.log(`${exists ? '✓' : '✗'} ${artifact}`);
        
        if (exists) {
            const stats = await fs.stat(artifactPath);
            console.log(`   Size: ${stats.size} bytes`);
            console.log(`   Last modified: ${stats.mtime}`);
        }
    }

    console.log('\n=== Build Verification Complete ===');
}

verifyBuild().catch(console.error); 